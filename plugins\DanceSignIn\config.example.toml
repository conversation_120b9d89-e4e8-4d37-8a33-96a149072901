[DanceSignIn]
enable = true
command = ["唱舞签到"]
command-format = "唱舞签到 - 获取唱舞全明星签到链接"

# 定时任务配置
[DanceSignIn.auto_signin]
enable = true  # 启用定时自动签到
times = ["08:30", "11:30", "17:30"]  # 定时发送时间，可以自定义
target_groups = [  # 目标群组列表
    "27852221909@chatroom",  # 示例群组ID 1
    "55878994168@chatroom",  # 示例群组ID 2
    "12345678901@chatroom"   # 示例群组ID 3
]

[DanceSignIn.rate_limit]
cooldown = 5  # 冷却时间（秒）

# 配置说明：
# 1. 将此文件重命名为 config.toml
# 2. 替换 target_groups 中的群组ID为实际的群组ID
# 3. 群组ID可以从机器人日志中获取，格式为：数字@chatroom
# 4. times 数组中的时间格式必须为 "HH:MM"
# 5. 如果不需要定时功能，将 auto_signin.enable 设为 false
# 6. 如果 target_groups 为空，定时任务不会执行
