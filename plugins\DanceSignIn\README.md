# 唱舞全明星签到插件

## 功能介绍

这个插件提供唱舞全明星的签到功能，支持手动签到和定时自动签到。

## 功能特性

### 1. 手动签到
- 在群聊中发送 `唱舞签到` 命令
- 机器人会发送签到链接卡片和小程序消息
- 用户点击即可进入签到页面

### 2. 定时自动签到
- 支持每天多个时间点自动发送签到消息
- 默认时间：8:30、11:30、17:30
- 可配置多个目标群组
- 自动向配置的群组发送签到链接

## 配置说明

### 基本配置
```toml
[DanceSignIn]
enable = true  # 启用插件
command = ["唱舞签到"]  # 触发命令
command-format = "唱舞签到 - 获取唱舞全明星签到链接"
```

### 定时任务配置
```toml
[DanceSignIn.auto_signin]
enable = true  # 启用定时自动签到
times = ["08:30", "11:30", "17:30"]  # 定时发送时间
target_groups = [  # 目标群组列表
    "27852221909@chatroom",  # 群组ID 1
    "55878994168@chatroom"   # 群组ID 2
]
```

### 限流配置
```toml
[DanceSignIn.rate_limit]
cooldown = 5  # 冷却时间（秒）
```

## 使用方法

### 1. 配置群组ID
1. 在目标群组中发送任意消息
2. 查看机器人日志，找到群组ID（格式如：`12345678@chatroom`）
3. 将群组ID添加到配置文件的 `target_groups` 列表中

### 2. 启用定时任务
1. 确保 `auto_signin.enable = true`
2. 配置 `times` 数组，格式为 `"HH:MM"`
3. 配置 `target_groups` 数组，添加目标群组ID
4. 重启机器人或重载插件

### 3. 手动触发
在任意群聊中发送 `唱舞签到` 即可获取签到链接

## 注意事项

1. **群组ID获取**：群组ID可以从机器人日志中获取，格式通常为 `数字@chatroom`
2. **时间格式**：定时时间必须使用 `"HH:MM"` 格式，如 `"08:30"`
3. **权限要求**：机器人需要在目标群组中有发送消息的权限
4. **发送间隔**：定时任务会在不同群组间添加2秒间隔，避免发送过快

## 日志说明

插件会输出详细的日志信息：
- 配置加载状态
- 定时任务设置情况
- 签到消息发送结果
- 错误信息和异常处理

## 故障排除

### 1. 定时任务不执行
- 检查 `auto_signin.enable` 是否为 `true`
- 检查 `target_groups` 是否配置了有效的群组ID
- 查看日志中是否有定时任务设置成功的信息

### 2. 消息发送失败
- 检查机器人是否在目标群组中
- 检查机器人是否有发送消息的权限
- 查看日志中的具体错误信息

### 3. 配置文件错误
- 检查TOML语法是否正确
- 确保字符串使用双引号包围
- 确保数组格式正确

## 版本信息

- 版本：1.0.0
- 作者：XYBot
- 描述：唱舞全明星签到插件
