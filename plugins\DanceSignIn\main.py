import asyncio
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase


class DanceSignInPlugin(PluginBase):
    description = "唱舞全明星签到插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DanceSignIn"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/DanceSignIn/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()


    def _load_config(self):
        """加载配置文件"""
        try:
            with open("plugins/DanceSignIn/config.toml", "rb") as f:
                config = tomllib.load(f)["DanceSignIn"]

            self.enable = config.get("enable", True)
            self.command = config.get("command", ["唱舞签到"])
            self.command_format = config.get("command-format", "唱舞签到 - 获取签到链接")

            # 签到链接
            self.signin_link = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect"

            # 定时任务配置
            auto_signin_config = config.get("auto_signin", {})
            self.auto_signin_enable = auto_signin_config.get("enable", False)
            self.auto_signin_times = auto_signin_config.get("times", ["08:30", "11:30", "17:30"])
            self.target_groups = auto_signin_config.get("target_groups", [])

            logger.info(f"[{self.plugin_name}] 定时任务配置: 启用={self.auto_signin_enable}, 时间={self.auto_signin_times}, 目标群组={len(self.target_groups)}个")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置加载失败: {e}")
            self.enable = False
            self.auto_signin_enable = False
            self.auto_signin_times = []
            self.target_groups = []





    def _get_signin_link(self) -> str:
        """获取签到链接"""
        return self.signin_link

    def _get_card_info(self) -> tuple:
        """获取卡片信息"""
        title = "唱舞全明星"
        description = "点击进入签到页面，领取专属福利"
        thumb_url = "https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0"

        return title, description, thumb_url

    async def _send_miniprogram_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送小程序消息 - 派对邀请函"""
        try:
            # 艹，这个XML格式真TM复杂，老王我给你整理了一下
            xml = f'''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>派对邀请函-签到解锁6周年专属称号</title>
<des>唱舞星愿站</des>
<username />
<action>view</action>
<type>33</type>
<showtype>0</showtype>
<content />
<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
<lowurl />
<forwardflag>0</forwardflag>
<dataurl />
<lowdataurl />
<contentattr>0</contentattr>
<appattach>
    <attachid />
    <cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>
    <cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
    <cdnthumblength>173353</cdnthumblength>
    <cdnthumbheight>576</cdnthumbheight>
    <cdnthumbwidth>720</cdnthumbwidth>
    <cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>
    <aeskey>f10bff006983850491a301a4cdb0c357</aeskey>
    <encryver>1</encryver>
    <fileext />
    <islargefilemsg>0</islargefilemsg>
</appattach>
<extinfo />
<androidsource>3</androidsource>
<sourceusername>gh_25eb09d7bc53@app</sourceusername>
<sourcedisplayname>唱舞星愿站</sourcedisplayname>
<commenturl />
<thumburl />
<mediatagname />
<messageaction><![CDATA[]]></messageaction>
<messageext><![CDATA[]]></messageext>
<weappinfo>
    <pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
    <version>16</version>
    <type>2</type>
    <weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
    <weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
    <shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
    <appservicetype>0</appservicetype>
    <secflagforsinglepagemode>0</secflagforsinglepagemode>
    <videopageinfo>
        <thumbwidth>720</thumbwidth>
        <thumbheight>576</thumbheight>
        <fromopensdk>0</fromopensdk>
    </videopageinfo>
    <wxaTradeCommentScore>0</wxaTradeCommentScore>
</weappinfo>
<statextstr />
<md5>69b76813fe0b2a04149aee01978e42f7</md5>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl></commenturl>'''

            # 发送小程序消息，这个SB消息类型是33
            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                33  # 小程序消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 小程序消息发送失败，这个憨批接口又抽风了")
                return False

            logger.info(f"[{self.plugin_name}] 小程序消息发送成功，msg_id: {new_msg_id}")
            return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 小程序消息发送异常，艹: {str(e)}")
            return False

    async def _process_signin_request(self, bot: WechatAPIClient, wxid: str, user_wxid: str) -> bool:
        """处理签到请求"""
        try:
            # 获取签到链接
            signin_link = self._get_signin_link()

            # 获取卡片信息
            title, description, thumb_url = self._get_card_info()

            # 发送链接卡片
            await bot.send_link_message(
                wxid,
                signin_link,
                title,
                description,
                thumb_url
            )

            # 等待一下再发送小程序消息，避免消息发送太快被吞
            await asyncio.sleep(1)

            # 发送小程序消息 - 派对邀请函
            miniprogram_success = await self._send_miniprogram_message(bot, wxid, user_wxid)

            if not miniprogram_success:
                logger.warning(f"[{self.plugin_name}] 小程序消息发送失败，但链接卡片已发送成功")

            return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送签到链接失败: {e}")
            return False

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = message["Content"].strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是签到命令
        if not any(content.startswith(cmd) for cmd in self.command):
            return

        try:
            # 处理签到请求
            success = await self._process_signin_request(bot, wxid, user_wxid)

            if not success:
                raise Exception("签到链接发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            await bot.send_at_message(wxid, "处理失败", [user_wxid])

    async def on_enable(self, bot=None):
        """插件启用时调用，动态设置定时任务时间"""
        await super().on_enable(bot)

        if self.auto_signin_enable and self.target_groups:
            try:
                # 为每个时间点设置定时任务
                from utils.decorators import scheduler

                for i, time_str in enumerate(self.auto_signin_times):
                    try:
                        hour, minute = map(int, time_str.split(':'))
                        job_id = f"{self.__class__.__module__}.{self.__class__.__name__}.auto_signin_task_{i}"

                        # 移除可能存在的旧任务
                        try:
                            scheduler.remove_job(job_id)
                        except:
                            pass

                        # 添加新的定时任务
                        scheduler.add_job(
                            self.auto_signin_task,
                            'cron',
                            hour=hour,
                            minute=minute,
                            args=[bot],
                            id=job_id
                        )

                        logger.info(f"[{self.plugin_name}] 已设置定时任务: {time_str} (任务ID: {job_id})")

                    except ValueError as e:
                        logger.error(f"[{self.plugin_name}] 时间格式错误 '{time_str}': {e}")

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 设置定时任务失败: {e}")
        else:
            if not self.auto_signin_enable:
                logger.info(f"[{self.plugin_name}] 定时任务未启用")
            elif not self.target_groups:
                logger.warning(f"[{self.plugin_name}] 定时任务已启用但未配置目标群组")

    async def auto_signin_task(self, bot: WechatAPIClient):
        """定时自动签到任务"""
        if not self.enable or not self.auto_signin_enable or not self.target_groups:
            return

        try:
            logger.info(f"[{self.plugin_name}] 开始执行定时签到任务，目标群组: {len(self.target_groups)}个")

            # 向每个目标群组发送签到消息
            for group_id in self.target_groups:
                try:
                    # 发送签到链接和小程序消息
                    success = await self._process_signin_request(bot, group_id, bot.wxid)

                    if success:
                        logger.info(f"[{self.plugin_name}] 定时签到消息发送成功: {group_id}")
                    else:
                        logger.error(f"[{self.plugin_name}] 定时签到消息发送失败: {group_id}")

                    # 群组间添加间隔，避免发送过快
                    await asyncio.sleep(2)

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 向群组 {group_id} 发送定时签到失败: {e}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 定时签到任务执行异常: {e}")
