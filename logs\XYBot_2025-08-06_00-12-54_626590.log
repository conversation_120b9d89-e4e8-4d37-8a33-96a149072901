2025-08-06 00:12:56 | SUCCESS | 读取主设置成功
2025-08-06 00:12:56 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-08-06 00:12:56 | INFO | 2025/08/06 00:12:56 GetRedisAddr: 127.0.0.1:6379
2025-08-06 00:12:56 | INFO | 2025/08/06 00:12:56 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-08-06 00:12:56 | INFO | 2025/08/06 00:12:56 Server start at :9000
2025-08-06 00:12:56 | SUCCESS | WechatAPI服务已启动
2025-08-06 00:12:57 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-08-06 00:12:57 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-08-06 00:12:57 | SUCCESS | 登录成功
2025-08-06 00:12:57 | SUCCESS | 已开启自动心跳
2025-08-06 00:12:57 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-06 00:12:57 | SUCCESS | 数据库初始化成功
2025-08-06 00:12:57 | SUCCESS | 定时任务已启动
2025-08-06 00:12:57 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-06 00:12:57 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:12:58 | INFO | 播客API初始化成功
2025-08-06 00:12:58 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:12:58 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:12:58 | DEBUG | [TempFileManager] 添加清理规则: default
2025-08-06 00:12:58 | DEBUG | [TempFileManager] 添加清理规则: images
2025-08-06 00:12:58 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-08-06 00:12:58 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-08-06 00:12:58 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-08-06 00:12:58 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-08-06 00:12:58 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-08-06 00:12:59 | INFO | [ChatSummary] 数据库初始化成功
2025-08-06 00:12:59 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-06 00:12:59 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-06 00:12:59 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-06 00:12:59 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-06 00:12:59 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-06 00:12:59 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-06 00:12:59 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-06 00:12:59 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-06 00:12:59 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-06 00:12:59 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-06 00:12:59 | DEBUG |   - 启用状态: True
2025-08-06 00:12:59 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-06 00:12:59 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-06 00:12:59 | DEBUG |   - Web ID: 7532989324985157172
2025-08-06 00:12:59 | DEBUG |   - Cookies配置: 已配置
2025-08-06 00:12:59 | DEBUG |   - 限制机制: 已禁用
2025-08-06 00:12:59 | DEBUG |   - 数字选择超时: 120秒
2025-08-06 00:12:59 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-06 00:12:59 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-06 00:12:59 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-06 00:12:59 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-06 00:12:59 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-06 00:12:59 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-06 00:12:59 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-06 00:12:59 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:12:59 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-06 00:12:59 | INFO | [RenameReminder] 开始启用插件...
2025-08-06 00:12:59 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-06 00:12:59 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-06 00:12:59 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-06 00:12:59 | INFO | 已设置检查间隔为 3600 秒
2025-08-06 00:12:59 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-06 00:12:59 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-06 00:13:00 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-06 00:13:00 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-06 00:13:00 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-06 00:13:00 | INFO | [VoiceMusicPlugin] 插件初始化完成
2025-08-06 00:13:01 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-06 00:13:01 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:13:01 | INFO | [yuanbao] 插件初始化完成
2025-08-06 00:13:01 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-06 00:13:01 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-06 00:13:01 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-06 00:13:01 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceMusicPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-08-06 00:13:01 | INFO | 处理堆积消息中
2025-08-06 00:13:01 | SUCCESS | 处理堆积消息完毕
2025-08-06 00:13:01 | SUCCESS | 开始处理消息
2025-08-06 00:16:06 | DEBUG | 收到消息: {'MsgId': 2039110219, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_if9bozh3yp522:\n？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410578, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_zM8fM70t|v1_7C1oSDfj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5570615622377197481, 'MsgSeq': 871430345}
2025-08-06 00:16:06 | INFO | 收到文本消息: 消息ID:2039110219 来自:27852221909@chatroom 发送人:wxid_if9bozh3yp522 @:[] 内容:？
2025-08-06 00:16:06 | DEBUG | [DouBaoImageToImage] 收到文本消息: '？' from wxid_if9bozh3yp522 in 27852221909@chatroom
2025-08-06 00:16:06 | DEBUG | [DouBaoImageToImage] 命令解析: ['？']
2025-08-06 00:16:06 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-06 00:16:06 | DEBUG | 处理消息内容: '？'
2025-08-06 00:16:06 | DEBUG | 消息内容 '？' 不匹配任何命令，忽略
2025-08-06 00:16:57 | DEBUG | 收到消息: {'MsgId': 175222623, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n重载所有插件'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410629, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_mzg8EbLK|v1_lQgieb4O</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 重载所有插件', 'NewMsgId': 3654873487632393745, 'MsgSeq': 871430346}
2025-08-06 00:16:57 | INFO | 收到文本消息: 消息ID:175222623 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:重载所有插件
2025-08-06 00:16:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '重载所有插件' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:16:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['重载所有插件']
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | [BaiduAgents] 插件已禁用,资源清理完成
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | [VoiceMusicPlugin] 插件已禁用
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | INFO | 已卸载定时任务: set()
2025-08-06 00:16:57 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-08-06 00:16:57 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:16:58 | INFO | 播客API初始化成功
2025-08-06 00:16:58 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:16:58 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-08-06 00:16:58 | INFO | [ChatSummary] 数据库初始化成功
2025-08-06 00:16:58 | INFO | [DouBaoImageToImage] ========== 初始化豆包图生图插件 ==========
2025-08-06 00:16:58 | DEBUG | [DouBaoImageToImage] 临时目录创建: temp\doubao_image_to_image
2025-08-06 00:16:58 | DEBUG | [DouBaoImageToImage] 开始加载配置...
2025-08-06 00:16:58 | INFO | [DouBaoImageToImage] 插件初始化完成
2025-08-06 00:16:58 | INFO | [DouBaoImageToImage] 支持 5 种比例，32 种风格
2025-08-06 00:16:58 | INFO | [DouBaoImageToImage] 插件状态: 启用
2025-08-06 00:16:58 | INFO | [DouBaoImageToImage] 冷却时间: 15秒
2025-08-06 00:16:58 | INFO | [DouBaoImageToImage] ========== 插件初始化完成 ==========
2025-08-06 00:16:58 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-08-06 00:16:58 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-08-06 00:16:58 | DEBUG |   - 启用状态: True
2025-08-06 00:16:58 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-08-06 00:16:58 | DEBUG |   - 设备ID: 7532989318484657699
2025-08-06 00:16:58 | DEBUG |   - Web ID: 7532989324985157172
2025-08-06 00:16:58 | DEBUG |   - Cookies配置: 已配置
2025-08-06 00:16:58 | DEBUG |   - 限制机制: 已禁用
2025-08-06 00:16:58 | DEBUG |   - 数字选择超时: 120秒
2025-08-06 00:16:58 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-08-06 00:16:58 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-08-06 00:16:58 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-08-06 00:16:58 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-08-06 00:16:58 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-08-06 00:16:58 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-08-06 00:16:58 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-08-06 00:16:58 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:16:58 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-08-06 00:16:58 | INFO | [RenameReminder] 开始启用插件...
2025-08-06 00:16:58 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-08-06 00:16:58 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-08-06 00:16:58 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-08-06 00:16:58 | INFO | 已设置检查间隔为 3600 秒
2025-08-06 00:16:58 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-08-06 00:16:59 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-08-06 00:17:00 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-08-06 00:17:00 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-08-06 00:17:00 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-08-06 00:17:00 | INFO | [VoiceMusicPlugin] 插件初始化完成
2025-08-06 00:17:00 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-08-06 00:17:00 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-08-06 00:17:00 | INFO | [yuanbao] 插件初始化完成
2025-08-06 00:17:00 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-08-06 00:17:00 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-08-06 00:17:00 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-08-06 00:17:00 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅所有插件重载成功
2025-08-06 00:17:00 | DEBUG | 处理消息内容: '重载所有插件'
2025-08-06 00:17:00 | DEBUG | 消息内容 '重载所有插件' 不匹配任何命令，忽略
2025-08-06 00:17:03 | DEBUG | 收到消息: {'MsgId': 2075698635, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 gogogo'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410635, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>11</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_yW/c/vQJ|v1_1IEKDh2q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 gogogo', 'NewMsgId': 2434586908212828523, 'MsgSeq': 871430349}
2025-08-06 00:17:03 | INFO | 收到文本消息: 消息ID:2075698635 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 gogogo
2025-08-06 00:17:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 gogogo' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:17:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', 'gogogo']
2025-08-06 00:17:03 | INFO | 成功加载表情映射文件，共 557 条记录
2025-08-06 00:17:03 | DEBUG | 处理消息内容: '语音点歌 gogogo'
2025-08-06 00:17:03 | DEBUG | 消息内容 '语音点歌 gogogo' 不匹配任何命令，忽略
2025-08-06 00:17:04 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '刘耀文-加油加油gogogo', 'singer': 'yy', 'url': 'http://ring.bssdlbig.kugou.com/ad320ae644bb2a47238baf8135e172be.mp3'}}
2025-08-06 00:17:05 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 00:17:05 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 47975 bytes
2025-08-06 00:17:06 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:3004 格式:mp3 音频base64略
2025-08-06 00:17:06 | INFO | [VoiceMusicPlugin] 成功发送音乐: 刘耀文-加油加油gogogo - yy
2025-08-06 00:17:19 | DEBUG | 收到消息: {'MsgId': 153945700, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="5e8ce6b1ffcb1c27ed2367faadb2c780" len = "11191" productid="" androidmd5="5e8ce6b1ffcb1c27ed2367faadb2c780" androidlen="11191" s60v3md5 = "5e8ce6b1ffcb1c27ed2367faadb2c780" s60v3len="11191" s60v5md5 = "5e8ce6b1ffcb1c27ed2367faadb2c780" s60v5len="11191" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=5e8ce6b1ffcb1c27ed2367faadb2c780&amp;filekey=30340201010420301e020201060402534804105e8ce6b1ffcb1c27ed2367faadb2c78002022bb7040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc3870008d6c8000000000000010600004f5053482b86db40b654daf7c&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=a7da7a5fb6d33601a15b2483b5eb4fbb&amp;filekey=30340201010420301e02020106040253480410a7da7a5fb6d33601a15b2483b5eb4fbb02022bc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc387000b507c000000000000010600004f505348279d2a00b65511cbf&amp;bizid=1023" aeskey= "572b4151f51d4c7ff84594c495b8e45e" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=8d09ec763877d508874687cbdb56fe05&amp;filekey=30340201010420301e020201060402534804108d09ec763877d508874687cbdb56fe0502021cc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc387000e2952000000000000010600004f5053482056fb40b65474de4&amp;bizid=1023" externmd5 = "2d93ebc2fd5a0200d1d16c87b28db76c" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410651, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_eSd5D9Sa|v1_K0VhQG3S</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8662230578770317046, 'MsgSeq': 871430352}
2025-08-06 00:17:19 | INFO | 收到表情消息: 消息ID:153945700 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:5e8ce6b1ffcb1c27ed2367faadb2c780 大小:11191
2025-08-06 00:17:19 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8662230578770317046
2025-08-06 00:18:35 | DEBUG | 收到消息: {'MsgId': 767767566, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 丑八怪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410727, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_Z/Il9lsn|v1_gdrvibdE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 丑八怪', 'NewMsgId': 5735691929061428586, 'MsgSeq': 871430353}
2025-08-06 00:18:35 | INFO | 收到文本消息: 消息ID:767767566 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 丑八怪
2025-08-06 00:18:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 丑八怪' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 00:18:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '丑八怪']
2025-08-06 00:18:35 | DEBUG | 处理消息内容: '语音点歌 丑八怪'
2025-08-06 00:18:35 | DEBUG | 消息内容 '语音点歌 丑八怪' 不匹配任何命令，忽略
2025-08-06 00:18:36 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '丑八怪', 'singer': '薛之谦', 'url': 'http://ring.bssdlbig.kugou.com/131a2c51f5a0f8065710e5cf38c197ca.mp3'}}
2025-08-06 00:18:37 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 00:18:37 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 322245 bytes
2025-08-06 00:18:39 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:40230 格式:mp3 音频base64略
2025-08-06 00:18:39 | INFO | [VoiceMusicPlugin] 成功发送音乐: 丑八怪 - 薛之谦
2025-08-06 00:18:51 | DEBUG | 收到消息: {'MsgId': 1868692576, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 丑八怪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410743, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ksBRSBFi|v1_SrIjh9Pn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5336541099647526675, 'MsgSeq': 871430356}
2025-08-06 00:18:51 | INFO | 收到文本消息: 消息ID:1868692576 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 丑八怪
2025-08-06 00:18:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 丑八怪' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:18:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '丑八怪']
2025-08-06 00:18:51 | DEBUG | 处理消息内容: '语音点歌 丑八怪'
2025-08-06 00:18:51 | DEBUG | 消息内容 '语音点歌 丑八怪' 不匹配任何命令，忽略
2025-08-06 00:18:52 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '丑八怪', 'singer': '薛之谦', 'url': 'http://ring.bssdlbig.kugou.com/131a2c51f5a0f8065710e5cf38c197ca.mp3'}}
2025-08-06 00:18:53 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 00:18:53 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 322245 bytes
2025-08-06 00:18:55 | INFO | 发送语音消息: 对方wxid:27852221909@chatroom 时长:40230 格式:mp3 音频base64略
2025-08-06 00:18:55 | INFO | [VoiceMusicPlugin] 成功发送音乐: 丑八怪 - 薛之谦
2025-08-06 00:19:46 | DEBUG | 收到消息: {'MsgId': 361127744, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n疯了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410798, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_LKEzawo6|v1_lR/TM74b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7206670828261863667, 'MsgSeq': 871430359}
2025-08-06 00:19:46 | INFO | 收到文本消息: 消息ID:361127744 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:疯了
2025-08-06 00:19:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '疯了' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:19:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['疯了']
2025-08-06 00:19:47 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:a8120cc98fb80fa4aae5948e3e330fb2 总长度:9992069
2025-08-06 00:19:47 | DEBUG | 处理消息内容: '疯了'
2025-08-06 00:19:47 | DEBUG | 消息内容 '疯了' 不匹配任何命令，忽略
2025-08-06 00:19:54 | DEBUG | 收到消息: {'MsgId': 2029068763, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410806, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_QShJqZNI|v1_wdzp4+pm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4999840549027170415, 'MsgSeq': 871430362}
2025-08-06 00:19:54 | INFO | 收到表情消息: 消息ID:2029068763 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[破涕为笑]
2025-08-06 00:19:54 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4999840549027170415
2025-08-06 00:20:02 | DEBUG | 收到消息: {'MsgId': 1647862398, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2700" length="3750" bufid="0" aeskey="1ea6eaeed9cce950fe8581605201b632" voiceurl="3052020100044b3049020100020472d8e1f002032f7793020458b8206f020468922f3e042436346565653437312d626330322d346339392d623261662d65663162353738646330623502040524000f0201000400" voicemd5="" clientmsgid="499d578f603b4ff37f9a5eb35cd2d01627852221909@chatroom_62547_1754410812" fromusername="wxid_3156oxt19n0x22" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410814, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/klIyREC|v1_bToJYGjI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5258603647030026927, 'MsgSeq': 871430363}
2025-08-06 00:20:02 | INFO | 收到语音消息: 消息ID:1647862398 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="2700" length="3750" bufid="0" aeskey="1ea6eaeed9cce950fe8581605201b632" voiceurl="3052020100044b3049020100020472d8e1f002032f7793020458b8206f020468922f3e042436346565653437312d626330322d346339392d623261662d65663162353738646330623502040524000f0201000400" voicemd5="" clientmsgid="499d578f603b4ff37f9a5eb35cd2d01627852221909@chatroom_62547_1754410812" fromusername="wxid_3156oxt19n0x22" /></msg>
2025-08-06 00:20:02 | DEBUG | [VoiceTest] 缓存语音 MsgId: 1647862398
2025-08-06 00:20:02 | DEBUG | [VoiceTest] 缓存语音 NewMsgId: 5258603647030026927
2025-08-06 00:20:02 | INFO | [VoiceTest] 已缓存语音消息: MsgId=1647862398, NewMsgId=5258603647030026927
2025-08-06 00:20:14 | DEBUG | 收到消息: {'MsgId': 382363312, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>跟着唱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>5258603647030026927</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_3156oxt19n0x22</chatusr>\n\t\t\t<displayname>LvemiQ⁸</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_zDpMyLBU|v1_XTAtzC+W&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>2700:1:\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754410814</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410826, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>69df7676459734178bf82fa66b062ca6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_lSiPfKDR|v1_d1qvQow7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 884269303546178498, 'MsgSeq': 871430364}
2025-08-06 00:20:14 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-06 00:20:14 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 00:20:14 | INFO | 收到引用消息: 消息ID:382363312 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 内容:跟着唱 引用类型:34
2025-08-06 00:20:14 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 00:20:14 | INFO | [DouBaoImageToImage] 消息内容: '跟着唱' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:20:14 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['跟着唱']
2025-08-06 00:20:14 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 00:20:14 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 00:20:14 | INFO |   - 消息内容: 跟着唱
2025-08-06 00:20:14 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 00:20:14 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-06 00:20:14 | INFO |   - 引用信息: {'MsgType': 34, 'Content': '2700:1:\n', 'Msgid': '5258603647030026927', 'NewMsgId': '5258603647030026927', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'LvemiQ⁸', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_zDpMyLBU|v1_XTAtzC+W</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754410814', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-06 00:20:14 | INFO |   - 引用消息ID: 
2025-08-06 00:20:14 | INFO |   - 引用消息类型: 
2025-08-06 00:20:14 | INFO |   - 引用消息内容: 2700:1:

2025-08-06 00:20:14 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-06 00:20:14 | INFO | [VoiceTest] 下载参数: msg_id=1647862398, voiceurl=3052020100044b3049020100020472d8e1f002032f77930204..., length=3750
2025-08-06 00:20:14 | DEBUG | [TempFileManager] 创建临时文件: C:\XYBotV2\temp\voice_test\5258603647030026927_mmzgucjc.silk
2025-08-06 00:20:15 | INFO | 发送语音消息: 对方wxid:27852221909@chatroom 时长:2700 格式:amr 音频base64略
2025-08-06 00:20:30 | DEBUG | 收到消息: {'MsgId': 269071073, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n[无语]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410842, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_EqI3EUHs|v1_a++p1R+G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7854226663049637973, 'MsgSeq': 871430367}
2025-08-06 00:20:30 | INFO | 收到表情消息: 消息ID:269071073 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:[无语]
2025-08-06 00:20:30 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7854226663049637973
2025-08-06 00:20:32 | DEBUG | 收到消息: {'MsgId': 852887379, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[奸笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410844, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_CYfTWxFD|v1_SmxRIeQ2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8390983441513799283, 'MsgSeq': 871430368}
2025-08-06 00:20:32 | INFO | 收到表情消息: 消息ID:852887379 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[奸笑]
2025-08-06 00:20:32 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8390983441513799283
2025-08-06 00:20:39 | DEBUG | 收到消息: {'MsgId': 137401854, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n撤回'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410851, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Q+1EhQcj|v1_kjZmVXzI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8765179046131784067, 'MsgSeq': 871430369}
2025-08-06 00:20:39 | INFO | 收到文本消息: 消息ID:137401854 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:撤回
2025-08-06 00:20:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '撤回' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:20:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['撤回']
2025-08-06 00:20:39 | DEBUG | 处理消息内容: '撤回'
2025-08-06 00:20:39 | DEBUG | 消息内容 '撤回' 不匹配任何命令，忽略
2025-08-06 00:20:48 | DEBUG | 收到消息: {'MsgId': 341588932, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>撤回</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>34</type>\n\t\t\t<svrid>5385835641515889756</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_AErxonrM|v1_vc2TV+S3&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>2700:1:\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754410827</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410860, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>8ab5e365a7a729528a3af8ce1f8dc30d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Tm62RYVP|v1_NhpTmMlB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7080667328971587779, 'MsgSeq': 871430370}
2025-08-06 00:20:48 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-06 00:20:48 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 00:20:48 | INFO | 收到引用消息: 消息ID:341588932 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 内容:撤回 引用类型:34
2025-08-06 00:20:48 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 00:20:48 | INFO | [DouBaoImageToImage] 消息内容: '撤回' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:20:48 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['撤回']
2025-08-06 00:20:48 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 00:20:49 | INFO | 消息撤回成功: 对方wxid:27852221909@chatroom ClientMsgId:1754410815 CreateTime:1754410828 NewMsgId:5385835641515889756
2025-08-06 00:20:49 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 00:20:49 | INFO |   - 消息内容: 撤回
2025-08-06 00:20:49 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 00:20:49 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-06 00:20:49 | INFO |   - 引用信息: {'MsgType': 34, 'Content': '2700:1:\n', 'Msgid': '5385835641515889756', 'NewMsgId': '5385835641515889756', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_AErxonrM|v1_vc2TV+S3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754410827', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-06 00:20:49 | INFO |   - 引用消息ID: 
2025-08-06 00:20:49 | INFO |   - 引用消息类型: 
2025-08-06 00:20:49 | INFO |   - 引用消息内容: 2700:1:

2025-08-06 00:20:49 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-06 00:20:49 | DEBUG | 收到消息: {'MsgId': 731292181, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': '27852221909@chatroom'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>331172484</msgid><newmsgid>5385835641515889756</newmsgid><replacemsg><![CDATA[你撤回了一条消息]]></replacemsg></revokemsg></sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410861, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3468313276, 'MsgSeq': 871430371}
2025-08-06 00:20:49 | DEBUG | 系统消息类型: revokemsg
2025-08-06 00:20:49 | INFO | 未知的系统消息类型: {'MsgId': 731292181, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>331172484</msgid><newmsgid>5385835641515889756</newmsgid><replacemsg><![CDATA[你撤回了一条消息]]></replacemsg></revokemsg></sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410861, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3468313276, 'MsgSeq': 871430371, 'FromWxid': '27852221909@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_4usgcju5ey9q29'}
2025-08-06 00:20:50 | DEBUG | 收到消息: {'MsgId': 649777655, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>1403837846</msgid><newmsgid>5258603647030026927</newmsgid><replacemsg><![CDATA["LvemiQ⁸" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410856, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5834736061701154887, 'MsgSeq': 871430372}
2025-08-06 00:20:50 | DEBUG | 系统消息类型: revokemsg
2025-08-06 00:20:50 | INFO | 未知的系统消息类型: {'MsgId': 649777655, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>1403837846</msgid><newmsgid>5258603647030026927</newmsgid><replacemsg><![CDATA["LvemiQ⁸" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410856, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5834736061701154887, 'MsgSeq': 871430372, 'FromWxid': '27852221909@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_3156oxt19n0x22'}
2025-08-06 00:21:04 | DEBUG | 收到消息: {'MsgId': 346098485, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="e944ebed8b226b88907f19e5529ec666" len="4047" productid="" androidmd5="e944ebed8b226b88907f19e5529ec666" androidlen="4047" s60v3md5="e944ebed8b226b88907f19e5529ec666" s60v3len="4047" s60v5md5="e944ebed8b226b88907f19e5529ec666" s60v5len="4047" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=e944ebed8b226b88907f19e5529ec666&amp;filekey=30340201010420301e020201060402535a0410e944ebed8b226b88907f19e5529ec66602020fcf040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306131366635393937383239326634313133356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=24ccffda6295324ca14c5fab9ac755ac&amp;filekey=30340201010420301e020201060402535a041024ccffda6295324ca14c5fab9ac755ac02020fd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306236646164393937383239326632323462356630393030303030313036&amp;bizid=1023" aeskey="a780a14c6b2eb763451574fb9805433b" externurl="" externmd5="" width="69" height="67" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410876, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VdRh8XAv|v1_M+0+ybej</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8051571163780194941, 'MsgSeq': 871430373}
2025-08-06 00:21:04 | INFO | 收到表情消息: 消息ID:346098485 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:e944ebed8b226b88907f19e5529ec666 大小:4047
2025-08-06 00:21:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8051571163780194941
2025-08-06 00:21:07 | DEBUG | 收到消息: {'MsgId': 840032227, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n取消撤回'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410879, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_p9pInRV3|v1_jadS8biF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8573028381435223573, 'MsgSeq': 871430374}
2025-08-06 00:21:07 | INFO | 收到文本消息: 消息ID:840032227 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:取消撤回
2025-08-06 00:21:07 | DEBUG | [DouBaoImageToImage] 收到文本消息: '取消撤回' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:21:07 | DEBUG | [DouBaoImageToImage] 命令解析: ['取消撤回']
2025-08-06 00:21:07 | DEBUG | 处理消息内容: '取消撤回'
2025-08-06 00:21:07 | DEBUG | 消息内容 '取消撤回' 不匹配任何命令，忽略
2025-08-06 00:21:26 | DEBUG | 收到消息: {'MsgId': 918800139, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n应该加一个取消撤回'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410898, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_wbDu0/iG|v1_qUKkgfoJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2917837952538844179, 'MsgSeq': 871430375}
2025-08-06 00:21:26 | INFO | 收到文本消息: 消息ID:918800139 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:应该加一个取消撤回
2025-08-06 00:21:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '应该加一个取消撤回' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:21:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['应该加一个取消撤回']
2025-08-06 00:21:26 | DEBUG | 处理消息内容: '应该加一个取消撤回'
2025-08-06 00:21:26 | DEBUG | 消息内容 '应该加一个取消撤回' 不匹配任何命令，忽略
2025-08-06 00:21:38 | DEBUG | 收到消息: {'MsgId': 688034536, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="314db052c1847c0b51794ce3eff22482" len = "5167" productid="" androidmd5="314db052c1847c0b51794ce3eff22482" androidlen="5167" s60v3md5 = "314db052c1847c0b51794ce3eff22482" s60v3len="5167" s60v5md5 = "314db052c1847c0b51794ce3eff22482" s60v5len="5167" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=314db052c1847c0b51794ce3eff22482&amp;filekey=30340201010420301e02020106040253480410314db052c1847c0b51794ce3eff224820202142f040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b5000ee6ad950c9c370000010600004f50534828034b00b6d05aeac&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=30521179addfd3e55a1a8baeb38feff6&amp;filekey=30340201010420301e0202010604025348041030521179addfd3e55a1a8baeb38feff602021430040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b60000df53950c9c370000010600004f5053481bd34b00b6cfd4d17&amp;bizid=1023" aeskey= "1cd9b41a5bdf1d6ba4d514bdc1e24df4" externurl = "" externmd5 = "" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410910, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_5H2DabBD|v1_qMPZZr+4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3270921134841055778, 'MsgSeq': 871430376}
2025-08-06 00:21:38 | INFO | 收到表情消息: 消息ID:688034536 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:314db052c1847c0b51794ce3eff22482 大小:5167
2025-08-06 00:21:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3270921134841055778
2025-08-06 00:22:00 | DEBUG | 收到消息: {'MsgId': 1744003027, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n[捂脸]你这样没人敢说话'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410931, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Lsk8Zbmy|v1_b+B76fqU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5994138884431573242, 'MsgSeq': 871430377}
2025-08-06 00:22:00 | INFO | 收到文本消息: 消息ID:1744003027 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:[捂脸]你这样没人敢说话
2025-08-06 00:22:00 | DEBUG | [DouBaoImageToImage] 收到文本消息: '[捂脸]你这样没人敢说话' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:22:00 | DEBUG | [DouBaoImageToImage] 命令解析: ['[捂脸]你这样没人敢说话']
2025-08-06 00:22:00 | DEBUG | 处理消息内容: '[捂脸]你这样没人敢说话'
2025-08-06 00:22:00 | DEBUG | 消息内容 '[捂脸]你这样没人敢说话' 不匹配任何命令，忽略
2025-08-06 00:22:08 | DEBUG | 收到消息: {'MsgId': 313673431, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>复读</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>8051571163780194941</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_3156oxt19n0x22</chatusr>\n\t\t\t<displayname>LvemiQ⁸</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_6HDuCgQ2|v1_leo60z1U&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media*#*0_0" md5="e944ebed8b226b88907f19e5529ec666" len="4047" productid="" androidmd5="e944ebed8b226b88907f19e5529ec666" androidlen="4047" s60v3md5="e944ebed8b226b88907f19e5529ec666" s60v3len="4047" s60v5md5="e944ebed8b226b88907f19e5529ec666" s60v5len="4047" cdnurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=e944ebed8b226b88907f19e5529ec666&amp;amp;filekey=30340201010420301e020201060402535a0410e944ebed8b226b88907f19e5529ec66602020fcf040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=32303231303632373137343935363030306131366635393937383239326634313133356630393030303030313036&amp;amp;bizid=1023" designerid="" thumburl="" encrypturl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=24ccffda6295324ca14c5fab9ac755ac&amp;amp;filekey=30340201010420301e020201060402535a041024ccffda6295324ca14c5fab9ac755ac02020fd0040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=32303231303632373137343935363030306236646164393937383239326632323462356630393030303030313036&amp;amp;bizid=1023" aeskey="a780a14c6b2eb763451574fb9805433b" externurl="" externmd5="" width="69" height="67" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""&gt;&lt;/emoji&gt;&lt;gameext type="0" content="0"&gt;&lt;/gameext&gt;&lt;/msg&gt;:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754410876</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410940, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>175805b474e8ba0e578acbbc6ad9bf77_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_2X4lLFfi|v1_NjxQ1wzl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2156232890477560290, 'MsgSeq': 871430378}
2025-08-06 00:22:08 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-08-06 00:22:08 | DEBUG | 检测到复读命令
2025-08-06 00:22:08 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 00:22:08 | INFO | 收到引用消息: 消息ID:313673431 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 内容:复读 引用类型:47
2025-08-06 00:22:08 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 00:22:08 | INFO | [DouBaoImageToImage] 消息内容: '复读' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:22:08 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['复读']
2025-08-06 00:22:08 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 00:22:08 | INFO | 成功保存表情映射文件，共 558 条记录
2025-08-06 00:22:09 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:e944ebed8b226b88907f19e5529ec666 总长度:4047
2025-08-06 00:22:09 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 00:22:09 | INFO |   - 消息内容: 复读
2025-08-06 00:22:09 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 00:22:09 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-08-06 00:22:09 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media*#*0_0" md5="e944ebed8b226b88907f19e5529ec666" len="4047" productid="" androidmd5="e944ebed8b226b88907f19e5529ec666" androidlen="4047" s60v3md5="e944ebed8b226b88907f19e5529ec666" s60v3len="4047" s60v5md5="e944ebed8b226b88907f19e5529ec666" s60v5len="4047" cdnurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=e944ebed8b226b88907f19e5529ec666&amp;filekey=30340201010420301e020201060402535a0410e944ebed8b226b88907f19e5529ec66602020fcf040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306131366635393937383239326634313133356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=24ccffda6295324ca14c5fab9ac755ac&amp;filekey=30340201010420301e020201060402535a041024ccffda6295324ca14c5fab9ac755ac02020fd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306236646164393937383239326632323462356630393030303030313036&amp;bizid=1023" aeskey="a780a14c6b2eb763451574fb9805433b" externurl="" externmd5="" width="69" height="67" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>:0\n', 'Msgid': '8051571163780194941', 'NewMsgId': '8051571163780194941', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'LvemiQ⁸', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_6HDuCgQ2|v1_leo60z1U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754410876', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-08-06 00:22:09 | INFO |   - 引用消息ID: 
2025-08-06 00:22:09 | INFO |   - 引用消息类型: 
2025-08-06 00:22:09 | INFO |   - 引用消息内容: <msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media*#*0_0" md5="e944ebed8b226b88907f19e5529ec666" len="4047" productid="" androidmd5="e944ebed8b226b88907f19e5529ec666" androidlen="4047" s60v3md5="e944ebed8b226b88907f19e5529ec666" s60v3len="4047" s60v5md5="e944ebed8b226b88907f19e5529ec666" s60v5len="4047" cdnurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=e944ebed8b226b88907f19e5529ec666&amp;filekey=30340201010420301e020201060402535a0410e944ebed8b226b88907f19e5529ec66602020fcf040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306131366635393937383239326634313133356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=24ccffda6295324ca14c5fab9ac755ac&amp;filekey=30340201010420301e020201060402535a041024ccffda6295324ca14c5fab9ac755ac02020fd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306236646164393937383239326632323462356630393030303030313036&amp;bizid=1023" aeskey="a780a14c6b2eb763451574fb9805433b" externurl="" externmd5="" width="69" height="67" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>:0

2025-08-06 00:22:09 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-08-06 00:22:25 | DEBUG | 收到消息: {'MsgId': 1683433181, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="a248893dc07069e149bb6cc4df023794" len="292178" productid="com.tencent.xin.emoticon.person.stiker_165760793352dff00f05992590" androidmd5="a248893dc07069e149bb6cc4df023794" androidlen="292178" s60v3md5="a248893dc07069e149bb6cc4df023794" s60v3len="292178" s60v5md5="a248893dc07069e149bb6cc4df023794" s60v5len="292178" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=a248893dc07069e149bb6cc4df023794&amp;filekey=30350201010421301f020201060402535a0410a248893dc07069e149bb6cc4df0237940203047552040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263022aae0003194515a812d00000010600004f50535a0740c950b6522ded7&amp;bizid=1023" designerid="" thumburl="http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLBpI7y0C4mp9iaIzKwXmTktVDeE81WOJRyAbSicDmc8Fh51Vd4vOJtFs9/0" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=652bd9fe5918df7a281b95ca54b881a5&amp;filekey=30350201010421301f020201060402535a0410652bd9fe5918df7a281b95ca54b881a50203047560040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263022aae0006651815a812d00000010600004f50535a2ae0c950b6ba6c298&amp;bizid=1023" aeskey="18e3c88eef49846cf5676045139a3fe0" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=2c763c6be4e35c5ca1029a2de2a003c3&amp;filekey=30340201010420301e020201060402535a04102c763c6be4e35c5ca1029a2de2a003c302022ae0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263022aae000844fc15a812d00000010600004f50535a2460c950b697ecbf6&amp;bizid=1023" externmd5="65cd18841a08966a6f0438ed0070dc58" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChQKB2RlZmF1bHQSCeW3sum7keWMlg=="></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410957, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_3waE/yyF|v1_t0P0JCMV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2493794397655409141, 'MsgSeq': 871430381}
2025-08-06 00:22:25 | INFO | 收到表情消息: 消息ID:1683433181 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:a248893dc07069e149bb6cc4df023794 大小:292178
2025-08-06 00:22:25 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2493794397655409141
2025-08-06 00:22:45 | DEBUG | 收到消息: {'MsgId': 46918569, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n大家都睡了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754410977, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_DSWUr3D1|v1_NNlf0iSq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2972179856244607463, 'MsgSeq': 871430382}
2025-08-06 00:22:45 | INFO | 收到文本消息: 消息ID:46918569 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:大家都睡了
2025-08-06 00:22:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '大家都睡了' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:22:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['大家都睡了']
2025-08-06 00:22:45 | DEBUG | 处理消息内容: '大家都睡了'
2025-08-06 00:22:45 | DEBUG | 消息内容 '大家都睡了' 不匹配任何命令，忽略
2025-08-06 00:23:26 | DEBUG | 收到消息: {'MsgId': 88969734, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n加一个瑶瑶附身，然后被附身的那个人不管发啥消息，就跟着发'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411018, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_9Y1AAiy+|v1_+9DfuIJA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7320863844120050868, 'MsgSeq': 871430383}
2025-08-06 00:23:26 | INFO | 收到文本消息: 消息ID:88969734 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:加一个瑶瑶附身，然后被附身的那个人不管发啥消息，就跟着发
2025-08-06 00:23:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '加一个瑶瑶附身，然后被附身的那个人不管发啥消息，就跟着发' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:23:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['加一个瑶瑶附身，然后被附身的那个人不管发啥消息，就跟着发']
2025-08-06 00:23:26 | DEBUG | 处理消息内容: '加一个瑶瑶附身，然后被附身的那个人不管发啥消息，就跟着发'
2025-08-06 00:23:26 | DEBUG | 消息内容 '加一个瑶瑶附身，然后被附身的那个人不管发啥消息，就跟着发' 不匹配任何命令，忽略
2025-08-06 00:23:33 | DEBUG | 收到消息: {'MsgId': 407447523, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="99ad225a3338891041c4c40162f5ff6d" len = "32290" productid="" androidmd5="99ad225a3338891041c4c40162f5ff6d" androidlen="32290" s60v3md5 = "99ad225a3338891041c4c40162f5ff6d" s60v3len="32290" s60v5md5 = "99ad225a3338891041c4c40162f5ff6d" s60v5len="32290" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=99ad225a3338891041c4c40162f5ff6d&amp;filekey=3043020101042f302d02016e040253480420393961643232356133333338383931303431633463343031363266356666366402027e22040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303035303266336134356561663133373031623964303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4c70bb97fd9b1c39b381ed32fbe63d5a&amp;filekey=3043020101042f302d02016e040253480420346337306262393766643962316333396233383165643332666265363364356102027e30040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303036343833376134356561663133373031623964303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "4da9e16429cb4c829199298e061c5af3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=396f13334a7ecfd217049b94d83a79d5&amp;filekey=3043020101042f302d02016e0402534804203339366631333333346137656366643231373034396239346438336137396435020214f0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303037343730376134356561663133373031623964303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "38c3f69d542f90c0646668d13a26fe5a" width= "227" height= "231" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgblnY/msLQ=" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411025, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_5cKvk53A|v1_p6eC4c2k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8371001428578938493, 'MsgSeq': 871430384}
2025-08-06 00:23:33 | INFO | 收到表情消息: 消息ID:407447523 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:99ad225a3338891041c4c40162f5ff6d 大小:32290
2025-08-06 00:23:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8371001428578938493
2025-08-06 00:23:54 | DEBUG | 收到消息: {'MsgId': 1941690527, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n你去私聊团长吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411046, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_TizvPX3z|v1_mfCW+g5d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9218603962079596439, 'MsgSeq': 871430385}
2025-08-06 00:23:54 | INFO | 收到文本消息: 消息ID:1941690527 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:你去私聊团长吧
2025-08-06 00:23:54 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你去私聊团长吧' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:23:54 | DEBUG | [DouBaoImageToImage] 命令解析: ['你去私聊团长吧']
2025-08-06 00:23:54 | DEBUG | 处理消息内容: '你去私聊团长吧'
2025-08-06 00:23:54 | DEBUG | 消息内容 '你去私聊团长吧' 不匹配任何命令，忽略
2025-08-06 00:24:08 | DEBUG | 收到消息: {'MsgId': 65738511, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n团长忙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411060, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_d8aynVHb|v1_6b5gVV/L</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9062758446661065027, 'MsgSeq': 871430386}
2025-08-06 00:24:08 | INFO | 收到文本消息: 消息ID:65738511 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:团长忙
2025-08-06 00:24:08 | DEBUG | [DouBaoImageToImage] 收到文本消息: '团长忙' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:24:08 | DEBUG | [DouBaoImageToImage] 命令解析: ['团长忙']
2025-08-06 00:24:08 | DEBUG | 处理消息内容: '团长忙'
2025-08-06 00:24:08 | DEBUG | 消息内容 '团长忙' 不匹配任何命令，忽略
2025-08-06 00:24:33 | DEBUG | 收到消息: {'MsgId': 710718066, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n我也忙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411085, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_iDFaY/ND|v1_KlsGTxbp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 134202189405231742, 'MsgSeq': 871430387}
2025-08-06 00:24:33 | INFO | 收到文本消息: 消息ID:710718066 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:我也忙
2025-08-06 00:24:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我也忙' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:24:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['我也忙']
2025-08-06 00:24:33 | DEBUG | 处理消息内容: '我也忙'
2025-08-06 00:24:33 | DEBUG | 消息内容 '我也忙' 不匹配任何命令，忽略
2025-08-06 00:24:43 | DEBUG | 收到消息: {'MsgId': 1570883749, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n忙着睡觉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411095, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_lj5HMwq/|v1_0hRECcRC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1971731952326365606, 'MsgSeq': 871430388}
2025-08-06 00:24:43 | INFO | 收到文本消息: 消息ID:1570883749 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:忙着睡觉
2025-08-06 00:24:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '忙着睡觉' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:24:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['忙着睡觉']
2025-08-06 00:24:43 | DEBUG | 处理消息内容: '忙着睡觉'
2025-08-06 00:24:43 | DEBUG | 消息内容 '忙着睡觉' 不匹配任何命令，忽略
2025-08-06 00:25:06 | DEBUG | 收到消息: {'MsgId': 1334883973, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n生前何必久睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411118, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_JxoyqhIi|v1_uva37tn7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8300188449146083347, 'MsgSeq': 871430389}
2025-08-06 00:25:06 | INFO | 收到文本消息: 消息ID:1334883973 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:生前何必久睡
2025-08-06 00:25:06 | DEBUG | [DouBaoImageToImage] 收到文本消息: '生前何必久睡' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:25:06 | DEBUG | [DouBaoImageToImage] 命令解析: ['生前何必久睡']
2025-08-06 00:25:06 | DEBUG | 处理消息内容: '生前何必久睡'
2025-08-06 00:25:06 | DEBUG | 消息内容 '生前何必久睡' 不匹配任何命令，忽略
2025-08-06 00:25:14 | DEBUG | [TempFileManager] 已清理文件: C:\XYBotV2\temp\voice_test\5258603647030026927_mmzgucjc.silk
2025-08-06 00:26:17 | DEBUG | 收到消息: {'MsgId': 1591607827, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n仙女不熬夜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411189, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_9JdPciwV|v1_Osee1FoT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2270519051399408125, 'MsgSeq': 871430390}
2025-08-06 00:26:17 | INFO | 收到文本消息: 消息ID:1591607827 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:仙女不熬夜
2025-08-06 00:26:17 | DEBUG | [DouBaoImageToImage] 收到文本消息: '仙女不熬夜' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:26:17 | DEBUG | [DouBaoImageToImage] 命令解析: ['仙女不熬夜']
2025-08-06 00:26:17 | DEBUG | 处理消息内容: '仙女不熬夜'
2025-08-06 00:26:17 | DEBUG | 消息内容 '仙女不熬夜' 不匹配任何命令，忽略
2025-08-06 00:26:39 | DEBUG | 收到消息: {'MsgId': 1091953220, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n说错了，是不睡觉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411211, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_9z8mKM9p|v1_rPA2u2AG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2054027325866973288, 'MsgSeq': 871430391}
2025-08-06 00:26:39 | INFO | 收到文本消息: 消息ID:1091953220 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:说错了，是不睡觉
2025-08-06 00:26:39 | DEBUG | [DouBaoImageToImage] 收到文本消息: '说错了，是不睡觉' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:26:39 | DEBUG | [DouBaoImageToImage] 命令解析: ['说错了，是不睡觉']
2025-08-06 00:26:39 | DEBUG | 处理消息内容: '说错了，是不睡觉'
2025-08-06 00:26:39 | DEBUG | 消息内容 '说错了，是不睡觉' 不匹配任何命令，忽略
2025-08-06 00:27:44 | DEBUG | 收到消息: {'MsgId': 550604523, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_zbh5p28da1si22:\n<msg><emoji fromusername = "wxid_zbh5p28da1si22" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="b8ad68dbb629ae3a552346912450b179" len = "6124768" productid="" androidmd5="b8ad68dbb629ae3a552346912450b179" androidlen="6124768" s60v3md5 = "b8ad68dbb629ae3a552346912450b179" s60v3len="6124768" s60v5md5 = "b8ad68dbb629ae3a552346912450b179" s60v5len="6124768" cdnurl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=b8ad68dbb629ae3a552346912450b179&amp;filekey=30440201010430302e02016e0402535a0420623861643638646262363239616533613535323334363931323435306231373902035d74e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26863cb51000573cd2ff31f900000006e01004fb2535a0a2931c1578bee149&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=c5c41191a07f45a7a8e033cc92cf3b0a&amp;filekey=30440201010430302e02016e0402535a0420633563343131393161303766343561376138653033336363393263663362306102035d74f0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26863cb51000affcb2ff31f900000006e02004fb2535a0a2931c1578bee1c0&amp;ef=2&amp;bizid=1022" aeskey= "7807fa0dce514764a415d8b6691dd761" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=2bf0aeb59114ba51ef05ee038d266d19&amp;filekey=30440201010430302e02016e0402535a04203262663061656235393131346261353165663035656530333864323636643139020303b880040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26863cb52000126272ff31f900000006e03004fb3535a0a2931c1578bee216&amp;ef=3&amp;bizid=1022" externmd5 = "028d52477c8c3615dac53e9393f750a0" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411276, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_8bdx/fyv|v1_Wh7ad+AD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 498717117898244843, 'MsgSeq': 871430392}
2025-08-06 00:27:44 | INFO | 收到表情消息: 消息ID:550604523 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 MD5:b8ad68dbb629ae3a552346912450b179 大小:6124768
2025-08-06 00:27:44 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 498717117898244843
2025-08-06 00:28:13 | DEBUG | 收到消息: {'MsgId': 1516979950, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n走'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411305, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_jjatmSRq|v1_q0ch7Lp4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5246030261655607062, 'MsgSeq': 871430393}
2025-08-06 00:28:13 | INFO | 收到文本消息: 消息ID:1516979950 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:走
2025-08-06 00:28:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '走' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:28:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['走']
2025-08-06 00:28:13 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:479d9683141301494753f07fd93dff19 总长度:9992069
2025-08-06 00:28:13 | DEBUG | 处理消息内容: '走'
2025-08-06 00:28:13 | DEBUG | 消息内容 '走' 不匹配任何命令，忽略
2025-08-06 00:28:49 | DEBUG | 收到消息: {'MsgId': 754273740, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n[偷笑]你自己玩吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411341, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_s2N0hgHh|v1_XPt6vX4i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9165261313464339641, 'MsgSeq': 871430396}
2025-08-06 00:28:49 | INFO | 收到文本消息: 消息ID:754273740 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:[偷笑]你自己玩吧
2025-08-06 00:28:49 | DEBUG | [DouBaoImageToImage] 收到文本消息: '[偷笑]你自己玩吧' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:28:49 | DEBUG | [DouBaoImageToImage] 命令解析: ['[偷笑]你自己玩吧']
2025-08-06 00:28:49 | DEBUG | 处理消息内容: '[偷笑]你自己玩吧'
2025-08-06 00:28:49 | DEBUG | 消息内容 '[偷笑]你自己玩吧' 不匹配任何命令，忽略
2025-08-06 00:29:35 | DEBUG | 收到消息: {'MsgId': 1942165409, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="5e8ce6b1ffcb1c27ed2367faadb2c780" len = "11191" productid="" androidmd5="5e8ce6b1ffcb1c27ed2367faadb2c780" androidlen="11191" s60v3md5 = "5e8ce6b1ffcb1c27ed2367faadb2c780" s60v3len="11191" s60v5md5 = "5e8ce6b1ffcb1c27ed2367faadb2c780" s60v5len="11191" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=5e8ce6b1ffcb1c27ed2367faadb2c780&amp;filekey=30340201010420301e020201060402534804105e8ce6b1ffcb1c27ed2367faadb2c78002022bb7040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc3870008d6c8000000000000010600004f5053482b86db40b654daf7c&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=a7da7a5fb6d33601a15b2483b5eb4fbb&amp;filekey=30340201010420301e02020106040253480410a7da7a5fb6d33601a15b2483b5eb4fbb02022bc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc387000b507c000000000000010600004f505348279d2a00b65511cbf&amp;bizid=1023" aeskey= "572b4151f51d4c7ff84594c495b8e45e" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=8d09ec763877d508874687cbdb56fe05&amp;filekey=30340201010420301e020201060402534804108d09ec763877d508874687cbdb56fe0502021cc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc387000e2952000000000000010600004f5053482056fb40b65474de4&amp;bizid=1023" externmd5 = "2d93ebc2fd5a0200d1d16c87b28db76c" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411387, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_n71fOgOY|v1_PTAA+Ig3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6320016854964187291, 'MsgSeq': 871430397}
2025-08-06 00:29:35 | INFO | 收到表情消息: 消息ID:1942165409 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:5e8ce6b1ffcb1c27ed2367faadb2c780 大小:11191
2025-08-06 00:29:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6320016854964187291
2025-08-06 00:30:20 | DEBUG | 收到消息: {'MsgId': 1020582076, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那就可能</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>997664716729316267</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_5vs4g3wmimgc22</chatusr>\n\t\t\t<displayname>刑蛋蛋</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;atuserlist&gt;wxid_2530z9t0joek22&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;alnode&gt;\n\t\t&lt;inlenlist&gt;15&lt;/inlenlist&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_RCNf//+y|v1_Nosl7O7T&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n@QvemiY¹_慕ؓ悦ؓ˒\u2005知道你是谁了mq 但是跟你挂的是这个号不是我</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754405198</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_2530z9t0joek22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411432, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a548b3941e6fad2181e1d12ee920477a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_nwY+g1qq|v1_8fZrGNJz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7579778323618887119, 'MsgSeq': 871430398}
2025-08-06 00:30:20 | DEBUG | 从群聊消息中提取发送者: wxid_2530z9t0joek22
2025-08-06 00:30:20 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 00:30:20 | INFO | 收到引用消息: 消息ID:1020582076 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 内容:那就可能 引用类型:1
2025-08-06 00:30:20 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 00:30:20 | INFO | [DouBaoImageToImage] 消息内容: '那就可能' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:30:20 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['那就可能']
2025-08-06 00:30:20 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 00:30:20 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 00:30:20 | INFO |   - 消息内容: 那就可能
2025-08-06 00:30:20 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 00:30:20 | INFO |   - 发送人: wxid_2530z9t0joek22
2025-08-06 00:30:20 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n@QvemiY¹_慕ؓ悦ؓ˒\u2005知道你是谁了mq 但是跟你挂的是这个号不是我', 'Msgid': '997664716729316267', 'NewMsgId': '997664716729316267', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '刑蛋蛋', 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_2530z9t0joek22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>15</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_RCNf//+y|v1_Nosl7O7T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754405198', 'SenderWxid': 'wxid_2530z9t0joek22'}
2025-08-06 00:30:20 | INFO |   - 引用消息ID: 
2025-08-06 00:30:20 | INFO |   - 引用消息类型: 
2025-08-06 00:30:20 | INFO |   - 引用消息内容: 
@QvemiY¹_慕ؓ悦ؓ˒ 知道你是谁了mq 但是跟你挂的是这个号不是我
2025-08-06 00:30:20 | INFO |   - 引用消息发送人: wxid_2530z9t0joek22
2025-08-06 00:30:27 | DEBUG | 收到消息: {'MsgId': 1127768166, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@LvemiQ⁸\u2005还不睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411439, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_3156oxt19n0x22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_MMnwp8yp|v1_MPRilw2f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3691995449371758413, 'MsgSeq': 871430399}
2025-08-06 00:30:27 | INFO | 收到文本消息: 消息ID:1127768166 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_3156oxt19n0x22'] 内容:@LvemiQ⁸ 还不睡
2025-08-06 00:30:27 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@LvemiQ⁸ 还不睡' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:30:27 | DEBUG | [DouBaoImageToImage] 命令解析: ['@LvemiQ⁸\u2005还不睡']
2025-08-06 00:30:27 | DEBUG | 处理消息内容: '@LvemiQ⁸ 还不睡'
2025-08-06 00:30:27 | DEBUG | 消息内容 '@LvemiQ⁸ 还不睡' 不匹配任何命令，忽略
2025-08-06 00:30:52 | DEBUG | 收到消息: {'MsgId': 1358153711, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@赵如初\u2005媳妇加好友[坏笑]你到底加不加'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411465, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_zbh5p28da1si22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gvSVsavj|v1_9em810Zl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1542113756279233513, 'MsgSeq': 871430400}
2025-08-06 00:30:52 | INFO | 收到文本消息: 消息ID:1358153711 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_zbh5p28da1si22'] 内容:@赵如初 媳妇加好友[坏笑]你到底加不加
2025-08-06 00:30:52 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@赵如初 媳妇加好友[坏笑]你到底加不加' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:30:52 | DEBUG | [DouBaoImageToImage] 命令解析: ['@赵如初\u2005媳妇加好友[坏笑]你到底加不加']
2025-08-06 00:30:52 | DEBUG | 处理消息内容: '@赵如初 媳妇加好友[坏笑]你到底加不加'
2025-08-06 00:30:52 | DEBUG | 消息内容 '@赵如初 媳妇加好友[坏笑]你到底加不加' 不匹配任何命令，忽略
2025-08-06 00:31:13 | DEBUG | 收到消息: {'MsgId': 1753601934, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n不加'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411485, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_eCOp32HR|v1_1buVhdxj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1057887189578299005, 'MsgSeq': 871430401}
2025-08-06 00:31:13 | INFO | 收到文本消息: 消息ID:1753601934 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:不加
2025-08-06 00:31:13 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不加' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:31:13 | DEBUG | [DouBaoImageToImage] 命令解析: ['不加']
2025-08-06 00:31:13 | DEBUG | 处理消息内容: '不加'
2025-08-06 00:31:13 | DEBUG | 消息内容 '不加' 不匹配任何命令，忽略
2025-08-06 00:31:25 | DEBUG | 收到消息: {'MsgId': 251429559, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@帅\u2005一边去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411497, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ubbh6q832tcs21]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gOXjVNCe|v1_Aka6Hyi1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5099975281721300772, 'MsgSeq': 871430402}
2025-08-06 00:31:25 | INFO | 收到文本消息: 消息ID:251429559 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_ubbh6q832tcs21'] 内容:@帅 一边去
2025-08-06 00:31:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@帅 一边去' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:31:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['@帅\u2005一边去']
2025-08-06 00:31:25 | DEBUG | 处理消息内容: '@帅 一边去'
2025-08-06 00:31:25 | DEBUG | 消息内容 '@帅 一边去' 不匹配任何命令，忽略
2025-08-06 00:31:45 | DEBUG | 收到消息: {'MsgId': 54784482, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>要睡觉了</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3691995449371758413</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>QvemiY¹_慕ؓ悦ؓ˒</displayname>\n\t\t\t<content>@LvemiQ⁸\u2005还不睡</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;873937979&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[wxid_3156oxt19n0x22]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_itT6yVjN|v1_BOX6e66I&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754411439</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_3156oxt19n0x22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411517, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>db427789f6accc8ca4d08fab3bad901f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_mDKIJ9pF|v1_KUlvhX1p</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4137599658013595619, 'MsgSeq': 871430403}
2025-08-06 00:31:45 | DEBUG | 从群聊消息中提取发送者: wxid_3156oxt19n0x22
2025-08-06 00:31:45 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 00:31:45 | INFO | 收到引用消息: 消息ID:54784482 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 内容:要睡觉了 引用类型:1
2025-08-06 00:31:45 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 00:31:45 | INFO | [DouBaoImageToImage] 消息内容: '要睡觉了' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:31:45 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['要睡觉了']
2025-08-06 00:31:45 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 00:31:45 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 00:31:45 | INFO |   - 消息内容: 要睡觉了
2025-08-06 00:31:45 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 00:31:45 | INFO |   - 发送人: wxid_3156oxt19n0x22
2025-08-06 00:31:45 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@LvemiQ⁸\u2005还不睡', 'Msgid': '3691995449371758413', 'NewMsgId': '3691995449371758413', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'QvemiY¹_慕ؓ悦ؓ˒', 'MsgSource': '<msgsource><sequence_id>873937979</sequence_id>\n\t<atuserlist><![CDATA[wxid_3156oxt19n0x22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_itT6yVjN|v1_BOX6e66I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754411439', 'SenderWxid': 'wxid_3156oxt19n0x22'}
2025-08-06 00:31:45 | INFO |   - 引用消息ID: 
2025-08-06 00:31:45 | INFO |   - 引用消息类型: 
2025-08-06 00:31:45 | INFO |   - 引用消息内容: @LvemiQ⁸ 还不睡
2025-08-06 00:31:45 | INFO |   - 引用消息发送人: wxid_3156oxt19n0x22
2025-08-06 00:31:51 | DEBUG | 收到消息: {'MsgId': 1788486040, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="3ac8822c1acbbbc596daf265ac85e8ef" len="96024" productid="" androidmd5="3ac8822c1acbbbc596daf265ac85e8ef" androidlen="96024" s60v3md5="3ac8822c1acbbbc596daf265ac85e8ef" s60v3len="96024" s60v5md5="3ac8822c1acbbbc596daf265ac85e8ef" s60v5len="96024" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=3ac8822c1acbbbc596daf265ac85e8ef&amp;filekey=30350201010421301f020201060402534804103ac8822c1acbbbc596daf265ac85e8ef0203017718040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630a161b0008dc30000000000000010600004f5053482166fb40b7126f617&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=56b8c9943be60f5486996b0d8345e079&amp;filekey=30350201010421301f020201060402535a041056b8c9943be60f5486996b0d8345e0790203017720040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2630a161b000e89e5000000000000010600004f50535a21b27880966196d36&amp;bizid=1023" aeskey="5a9e97e4d8e6d4e183c71d5947e306bc" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=d9f7a652b06e43796f042b9464124af9&amp;filekey=30350201010421301f02020106040253480410d9f7a652b06e43796f042b9464124af90203009920040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630a162000035ac6000000000000010600004f5053480e667b40b71372605&amp;bizid=1023" externmd5="2571c7dea1c745dbbae52e25ef39b0e5" width="88" height="88" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411523, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_DcBbwV1J|v1_6I4Dmbry</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7250329238738094795, 'MsgSeq': 871430404}
2025-08-06 00:31:51 | INFO | 收到表情消息: 消息ID:1788486040 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:3ac8822c1acbbbc596daf265ac85e8ef 大小:96024
2025-08-06 00:31:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7250329238738094795
2025-08-06 00:31:58 | DEBUG | 收到消息: {'MsgId': 1252867390, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@LvemiQ⁸\u2005还早呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411530, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_3156oxt19n0x22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_+J3W/gx6|v1_hKZRKsv4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2682806846167931373, 'MsgSeq': 871430405}
2025-08-06 00:31:58 | INFO | 收到文本消息: 消息ID:1252867390 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_3156oxt19n0x22'] 内容:@LvemiQ⁸ 还早呢
2025-08-06 00:31:58 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@LvemiQ⁸ 还早呢' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:31:58 | DEBUG | [DouBaoImageToImage] 命令解析: ['@LvemiQ⁸\u2005还早呢']
2025-08-06 00:31:58 | DEBUG | 处理消息内容: '@LvemiQ⁸ 还早呢'
2025-08-06 00:31:58 | DEBUG | 消息内容 '@LvemiQ⁸ 还早呢' 不匹配任何命令，忽略
2025-08-06 00:32:01 | DEBUG | 收到消息: {'MsgId': 67798774, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n晚安@QvemiY¹_慕ؓ悦ؓ˒\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411531, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_2530z9t0joek22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>15</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Z8EPmjGN|v1_XVkPVa/B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5153440162134817251, 'MsgSeq': 871430406}
2025-08-06 00:32:01 | INFO | 收到文本消息: 消息ID:67798774 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:['wxid_2530z9t0joek22'] 内容:晚安@QvemiY¹_慕ؓ悦ؓ˒ 
2025-08-06 00:32:01 | DEBUG | [DouBaoImageToImage] 收到文本消息: '晚安@QvemiY¹_慕ؓ悦ؓ˒' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:32:01 | DEBUG | [DouBaoImageToImage] 命令解析: ['晚安@QvemiY¹_慕ؓ悦ؓ˒']
2025-08-06 00:32:01 | DEBUG | 处理消息内容: '晚安@QvemiY¹_慕ؓ悦ؓ˒'
2025-08-06 00:32:01 | DEBUG | 消息内容 '晚安@QvemiY¹_慕ؓ悦ؓ˒' 不匹配任何命令，忽略
2025-08-06 00:32:11 | DEBUG | 收到消息: {'MsgId': 1630654504, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@LvemiQ⁸\u2005做梦去吧晚安'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411543, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_3156oxt19n0x22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_bF79qErw|v1_7pRbCHV5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5072737898140737663, 'MsgSeq': 871430407}
2025-08-06 00:32:11 | INFO | 收到文本消息: 消息ID:1630654504 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_3156oxt19n0x22'] 内容:@LvemiQ⁸ 做梦去吧晚安
2025-08-06 00:32:11 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@LvemiQ⁸ 做梦去吧晚安' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:32:11 | DEBUG | [DouBaoImageToImage] 命令解析: ['@LvemiQ⁸\u2005做梦去吧晚安']
2025-08-06 00:32:11 | DEBUG | 处理消息内容: '@LvemiQ⁸ 做梦去吧晚安'
2025-08-06 00:32:11 | DEBUG | 消息内容 '@LvemiQ⁸ 做梦去吧晚安' 不匹配任何命令，忽略
2025-08-06 00:32:23 | DEBUG | 收到消息: {'MsgId': 577804492, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@QvemiY¹_慕ؓ悦ؓ˒\u2005好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411555, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_2530z9t0joek22]]></atuserlist>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>15</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_f+VF0Uel|v1_LKLD1QpH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 239315429997933638, 'MsgSeq': 871430408}
2025-08-06 00:32:23 | INFO | 收到文本消息: 消息ID:577804492 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_2530z9t0joek22'] 内容:@QvemiY¹_慕ؓ悦ؓ˒ 好
2025-08-06 00:32:23 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@QvemiY¹_慕ؓ悦ؓ˒ 好' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-06 00:32:23 | DEBUG | [DouBaoImageToImage] 命令解析: ['@QvemiY¹_慕ؓ悦ؓ˒\u2005好']
2025-08-06 00:32:23 | DEBUG | 处理消息内容: '@QvemiY¹_慕ؓ悦ؓ˒ 好'
2025-08-06 00:32:23 | DEBUG | 消息内容 '@QvemiY¹_慕ؓ悦ؓ˒ 好' 不匹配任何命令，忽略
2025-08-06 00:32:25 | DEBUG | 收到消息: {'MsgId': 524207405, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我的夜生活才开始呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411558, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_LCW3Cerd|v1_Ebh2VpdF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5375642282231397420, 'MsgSeq': 871430409}
2025-08-06 00:32:25 | INFO | 收到文本消息: 消息ID:524207405 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我的夜生活才开始呢
2025-08-06 00:32:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我的夜生活才开始呢' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:32:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['我的夜生活才开始呢']
2025-08-06 00:32:25 | DEBUG | 处理消息内容: '我的夜生活才开始呢'
2025-08-06 00:32:25 | DEBUG | 消息内容 '我的夜生活才开始呢' 不匹配任何命令，忽略
2025-08-06 00:32:28 | DEBUG | 收到消息: {'MsgId': 1354043685, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n@QvemiY¹_慕ؓ悦ؓ˒\u2005你也睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411558, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_2530z9t0joek22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>15</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_lLmyPjZV|v1_lr7BK5et</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3096446878313865217, 'MsgSeq': 871430410}
2025-08-06 00:32:28 | INFO | 收到文本消息: 消息ID:1354043685 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:['wxid_2530z9t0joek22'] 内容:@QvemiY¹_慕ؓ悦ؓ˒ 你也睡
2025-08-06 00:32:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@QvemiY¹_慕ؓ悦ؓ˒ 你也睡' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:32:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['@QvemiY¹_慕ؓ悦ؓ˒\u2005你也睡']
2025-08-06 00:32:28 | DEBUG | 处理消息内容: '@QvemiY¹_慕ؓ悦ؓ˒ 你也睡'
2025-08-06 00:32:28 | DEBUG | 消息内容 '@QvemiY¹_慕ؓ悦ؓ˒ 你也睡' 不匹配任何命令，忽略
2025-08-06 00:32:31 | DEBUG | 收到消息: {'MsgId': 2011567978, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我不睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411563, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_dNs5p5Y0|v1_2+S6Frnh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7431813574293148209, 'MsgSeq': 871430411}
2025-08-06 00:32:31 | INFO | 收到文本消息: 消息ID:2011567978 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我不睡
2025-08-06 00:32:31 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我不睡' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:32:31 | DEBUG | [DouBaoImageToImage] 命令解析: ['我不睡']
2025-08-06 00:32:31 | DEBUG | 处理消息内容: '我不睡'
2025-08-06 00:32:31 | DEBUG | 消息内容 '我不睡' 不匹配任何命令，忽略
2025-08-06 00:32:34 | DEBUG | 收到消息: {'MsgId': 1311479003, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n修仙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411566, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Cg3NngwX|v1_tJI+7qJo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6978032210708465816, 'MsgSeq': 871430412}
2025-08-06 00:32:34 | INFO | 收到文本消息: 消息ID:1311479003 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:修仙
2025-08-06 00:32:34 | DEBUG | [DouBaoImageToImage] 收到文本消息: '修仙' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:32:34 | DEBUG | [DouBaoImageToImage] 命令解析: ['修仙']
2025-08-06 00:32:34 | DEBUG | 处理消息内容: '修仙'
2025-08-06 00:32:34 | DEBUG | 消息内容 '修仙' 不匹配任何命令，忽略
2025-08-06 00:32:36 | DEBUG | 收到消息: {'MsgId': 840331940, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n我现在上线通过'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411567, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_cUxNXrT3|v1_MA5v+mSH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8802527350025241491, 'MsgSeq': 871430413}
2025-08-06 00:32:36 | INFO | 收到文本消息: 消息ID:840331940 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:我现在上线通过
2025-08-06 00:32:36 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我现在上线通过' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-06 00:32:36 | DEBUG | [DouBaoImageToImage] 命令解析: ['我现在上线通过']
2025-08-06 00:32:36 | DEBUG | 处理消息内容: '我现在上线通过'
2025-08-06 00:32:36 | DEBUG | 消息内容 '我现在上线通过' 不匹配任何命令，忽略
2025-08-06 00:32:41 | DEBUG | 收到消息: {'MsgId': 282131012, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="bd280c44e097adf1fa54101b8e40da88" len="5268" productid="" androidmd5="bd280c44e097adf1fa54101b8e40da88" androidlen="5268" s60v3md5="bd280c44e097adf1fa54101b8e40da88" s60v3len="5268" s60v5md5="bd280c44e097adf1fa54101b8e40da88" s60v5len="5268" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=bd280c44e097adf1fa54101b8e40da88&amp;filekey=3043020101042f302d02016e040253480420626432383063343465303937616466316661353431303162386534306461383802021494040d00000004627466730000000132&amp;hy=SH&amp;storeid=2654200ad00052b213449ad590000006e01004fb153482bb60b01e70dffab8&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d8f85621af943f4b625b98b77bdca695&amp;filekey=3043020101042f302d02016e0402534804206438663835363231616639343366346236323562393862373762646361363935020214a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2654200ad0005b9243449ad590000006e02004fb253482bb60b01e70dffac7&amp;ef=2&amp;bizid=1022" aeskey="4d633160e4af462898dee85c79c23a51" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=bbdcddbbafc4d5806fdc30ee2054a8d5&amp;filekey=3043020101042f302d02016e040253480420626264636464626261666334643538303666646333306565323035346138643502020610040d00000004627466730000000132&amp;hy=SH&amp;storeid=2654200ad000643413449ad590000006e03004fb353482bb60b01e70dffad6&amp;ef=3&amp;bizid=1022" externmd5="24d213b635c7eed26a95e81e469f6435" width="155" height="149" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411573, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_4hXtxdyG|v1_Te29Bw2y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5121821475753378637, 'MsgSeq': 871430414}
2025-08-06 00:32:41 | INFO | 收到表情消息: 消息ID:282131012 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:bd280c44e097adf1fa54101b8e40da88 大小:5268
2025-08-06 00:32:41 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5121821475753378637
2025-08-06 00:32:42 | DEBUG | 收到消息: {'MsgId': 692583514, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@赵如初\u2005好好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411574, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_zbh5p28da1si22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_FuUtLwe0|v1_k9XIDtcd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1162828243112400101, 'MsgSeq': 871430415}
2025-08-06 00:32:42 | INFO | 收到文本消息: 消息ID:692583514 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_zbh5p28da1si22'] 内容:@赵如初 好好
2025-08-06 00:32:42 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@赵如初 好好' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:32:42 | DEBUG | [DouBaoImageToImage] 命令解析: ['@赵如初\u2005好好']
2025-08-06 00:32:42 | DEBUG | 处理消息内容: '@赵如初 好好'
2025-08-06 00:32:42 | DEBUG | 消息内容 '@赵如初 好好' 不匹配任何命令，忽略
2025-08-06 00:32:45 | DEBUG | 收到消息: {'MsgId': 1528935197, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我也上'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411577, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_NeobbtVL|v1_YLhVb4hh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4476918771271879398, 'MsgSeq': 871430416}
2025-08-06 00:32:45 | INFO | 收到文本消息: 消息ID:1528935197 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我也上
2025-08-06 00:32:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我也上' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:32:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['我也上']
2025-08-06 00:32:45 | DEBUG | 处理消息内容: '我也上'
2025-08-06 00:32:45 | DEBUG | 消息内容 '我也上' 不匹配任何命令，忽略
2025-08-06 00:33:02 | DEBUG | 收到消息: {'MsgId': 1867642315, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我失眠更年期了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411594, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_17o/5EWY|v1_LQKpK2ug</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5026582689993594655, 'MsgSeq': 871430417}
2025-08-06 00:33:02 | INFO | 收到文本消息: 消息ID:1867642315 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我失眠更年期了
2025-08-06 00:33:02 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我失眠更年期了' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:33:02 | DEBUG | [DouBaoImageToImage] 命令解析: ['我失眠更年期了']
2025-08-06 00:33:02 | DEBUG | 处理消息内容: '我失眠更年期了'
2025-08-06 00:33:02 | DEBUG | 消息内容 '我失眠更年期了' 不匹配任何命令，忽略
2025-08-06 00:33:19 | DEBUG | 收到消息: {'MsgId': 1434758193, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n@LvemiQ⁸\u2005看到没，不睡觉叫修仙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411611, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_3156oxt19n0x22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_hAFUMZiA|v1_j1aPc1+f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 114106273941864277, 'MsgSeq': 871430418}
2025-08-06 00:33:19 | INFO | 收到文本消息: 消息ID:1434758193 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:['wxid_3156oxt19n0x22'] 内容:@LvemiQ⁸ 看到没，不睡觉叫修仙
2025-08-06 00:33:19 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@LvemiQ⁸ 看到没，不睡觉叫修仙' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:33:19 | DEBUG | [DouBaoImageToImage] 命令解析: ['@LvemiQ⁸\u2005看到没，不睡觉叫修仙']
2025-08-06 00:33:19 | DEBUG | 处理消息内容: '@LvemiQ⁸ 看到没，不睡觉叫修仙'
2025-08-06 00:33:19 | DEBUG | 消息内容 '@LvemiQ⁸ 看到没，不睡觉叫修仙' 不匹配任何命令，忽略
2025-08-06 00:33:35 | DEBUG | 收到消息: {'MsgId': 1150009022, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="e944ebed8b226b88907f19e5529ec666" len="4047" productid="" androidmd5="e944ebed8b226b88907f19e5529ec666" androidlen="4047" s60v3md5="e944ebed8b226b88907f19e5529ec666" s60v3len="4047" s60v5md5="e944ebed8b226b88907f19e5529ec666" s60v5len="4047" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=e944ebed8b226b88907f19e5529ec666&amp;filekey=30340201010420301e020201060402535a0410e944ebed8b226b88907f19e5529ec66602020fcf040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306131366635393937383239326634313133356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=24ccffda6295324ca14c5fab9ac755ac&amp;filekey=30340201010420301e020201060402535a041024ccffda6295324ca14c5fab9ac755ac02020fd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632373137343935363030306236646164393937383239326632323462356630393030303030313036&amp;bizid=1023" aeskey="a780a14c6b2eb763451574fb9805433b" externurl="" externmd5="" width="69" height="67" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411627, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_d8BNM6G+|v1_aGeXUbAW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6166042280629429938, 'MsgSeq': 871430419}
2025-08-06 00:33:35 | INFO | 收到表情消息: 消息ID:1150009022 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:e944ebed8b226b88907f19e5529ec666 大小:4047
2025-08-06 00:33:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6166042280629429938
2025-08-06 00:33:43 | DEBUG | 收到消息: {'MsgId': 710153665, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n你不熬夜算哪门子仙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411635, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_P/EULp50|v1_IJ5oMf7i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6325405595725247192, 'MsgSeq': 871430420}
2025-08-06 00:33:43 | INFO | 收到文本消息: 消息ID:710153665 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:你不熬夜算哪门子仙
2025-08-06 00:33:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '你不熬夜算哪门子仙' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 00:33:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['你不熬夜算哪门子仙']
2025-08-06 00:33:43 | DEBUG | 处理消息内容: '你不熬夜算哪门子仙'
2025-08-06 00:33:43 | DEBUG | 消息内容 '你不熬夜算哪门子仙' 不匹配任何命令，忽略
2025-08-06 00:33:43 | DEBUG | 收到消息: {'MsgId': 1590656494, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n一起'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411635, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_ou7JkU83|v1_Ogm9ZCcd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7705051615436263271, 'MsgSeq': 871430421}
2025-08-06 00:33:43 | INFO | 收到文本消息: 消息ID:1590656494 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:一起
2025-08-06 00:33:43 | DEBUG | [DouBaoImageToImage] 收到文本消息: '一起' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:33:43 | DEBUG | [DouBaoImageToImage] 命令解析: ['一起']
2025-08-06 00:33:43 | DEBUG | 处理消息内容: '一起'
2025-08-06 00:33:43 | DEBUG | 消息内容 '一起' 不匹配任何命令，忽略
2025-08-06 00:33:51 | DEBUG | 收到消息: {'MsgId': 107383928, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n我是仙女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411643, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_QuqEkUuA|v1_TF1qrg9G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5714953746990546357, 'MsgSeq': 871430422}
2025-08-06 00:33:51 | INFO | 收到文本消息: 消息ID:107383928 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:我是仙女
2025-08-06 00:33:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我是仙女' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:33:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['我是仙女']
2025-08-06 00:33:51 | DEBUG | 处理消息内容: '我是仙女'
2025-08-06 00:33:51 | DEBUG | 消息内容 '我是仙女' 不匹配任何命令，忽略
2025-08-06 00:34:03 | DEBUG | 收到消息: {'MsgId': 208348288, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n不用修仙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411655, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_8Hr8HdyB|v1_U16VsR1S</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6075151746290034951, 'MsgSeq': 871430423}
2025-08-06 00:34:03 | INFO | 收到文本消息: 消息ID:208348288 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:[] 内容:不用修仙
2025-08-06 00:34:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '不用修仙' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:34:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['不用修仙']
2025-08-06 00:34:03 | DEBUG | 处理消息内容: '不用修仙'
2025-08-06 00:34:03 | DEBUG | 消息内容 '不用修仙' 不匹配任何命令，忽略
2025-08-06 00:34:19 | DEBUG | 收到消息: {'MsgId': 1416763573, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_3156oxt19n0x22:\n@帅\u2005再艾特我就把你炖了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411671, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ubbh6q832tcs21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_j2e1bV96|v1_oIbYudxL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4333231221105951160, 'MsgSeq': 871430424}
2025-08-06 00:34:19 | INFO | 收到文本消息: 消息ID:1416763573 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 @:['wxid_ubbh6q832tcs21'] 内容:@帅 再艾特我就把你炖了
2025-08-06 00:34:19 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@帅 再艾特我就把你炖了' from wxid_3156oxt19n0x22 in 27852221909@chatroom
2025-08-06 00:34:19 | DEBUG | [DouBaoImageToImage] 命令解析: ['@帅\u2005再艾特我就把你炖了']
2025-08-06 00:34:19 | DEBUG | 处理消息内容: '@帅 再艾特我就把你炖了'
2025-08-06 00:34:19 | DEBUG | 消息内容 '@帅 再艾特我就把你炖了' 不匹配任何命令，忽略
2025-08-06 00:34:21 | DEBUG | 收到消息: {'MsgId': 1005200956, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n我在狮子3'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411671, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_djKumJnx|v1_kRyUd8mf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5721776747141939290, 'MsgSeq': 871430425}
2025-08-06 00:34:21 | INFO | 收到文本消息: 消息ID:1005200956 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:我在狮子3
2025-08-06 00:34:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '我在狮子3' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:34:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['我在狮子3']
2025-08-06 00:34:21 | DEBUG | 处理消息内容: '我在狮子3'
2025-08-06 00:34:21 | DEBUG | 消息内容 '我在狮子3' 不匹配任何命令，忽略
2025-08-06 00:34:23 | DEBUG | 收到消息: {'MsgId': 265425175, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="75168a52308f60e1d492bf3a404d0a86" len="65158" productid="com.tencent.xin.emoticon.person.stiker_1505803978c14eef841512146c" androidmd5="75168a52308f60e1d492bf3a404d0a86" androidlen="65158" s60v3md5="75168a52308f60e1d492bf3a404d0a86" s60v3len="65158" s60v5md5="75168a52308f60e1d492bf3a404d0a86" s60v5len="65158" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=75168a52308f60e1d492bf3a404d0a86&amp;filekey=30350201010421301f020201060402535a041075168a52308f60e1d492bf3a404d0a86020300fe86040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263709448000dcf9506a8a5bf0000010600004f50535a21682910b6f69b919&amp;bizid=1023" designerid="" thumburl="http://wxapp.tc.qq.com/275/20304/stodownload?m=84e80d96fec9467a96c3962193a481e9&amp;filekey=30340201010420301e0202011304025348041084e80d96fec9467a96c3962193a481e902023791040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479687800074f4e000000000000011300004f5053480e01a8e0b68073645&amp;bizid=1023" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=4fc6931120e79bdc5d4152e903557228&amp;filekey=30350201010421301f020201060402535a04104fc6931120e79bdc5d4152e903557228020300fe90040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263709449000083b506a8a5bf0000010600004f50535a2d896910b7bae2a90&amp;bizid=1023" aeskey="c190aa17efd64df7a4d40b65d841394b" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=480d3806e2da640707643933f9dbbd87&amp;filekey=30340201010420301e020201060402535a0410480d3806e2da640707643933f9dbbd87020231a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2637094490002048b06a8a5bf0000010600004f50535a2dc0c950b6a401bf3&amp;bizid=1023" externmd5="b8805b867ad16bc98367aae3f66ab004" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChUKBXpoX2NuEgznha7kuobkvaDlk6YKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA="></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411672, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gmww7DM+|v1_9zUi8lY7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2390227332781133273, 'MsgSeq': 871430426}
2025-08-06 00:34:23 | INFO | 收到表情消息: 消息ID:265425175 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:75168a52308f60e1d492bf3a404d0a86 大小:65158
2025-08-06 00:34:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2390227332781133273
2025-08-06 00:34:24 | DEBUG | 收到消息: {'MsgId': 1508762972, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="76e3a504110290e7f7bea10597d5096f" len = "54886" productid="" androidmd5="76e3a504110290e7f7bea10597d5096f" androidlen="54886" s60v3md5 = "76e3a504110290e7f7bea10597d5096f" s60v3len="54886" s60v5md5 = "76e3a504110290e7f7bea10597d5096f" s60v5len="54886" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=76e3a504110290e7f7bea10597d5096f&amp;filekey=30440201010430302e02016e0402535a04203736653361353034313130323930653766376265613130353937643530393666020300d666040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26468cccf000796613b558e690000006e01004fb1535a17f81970b7234ff1d&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=e0e3613afae8407d47ad219c77c06277&amp;filekey=30440201010430302e02016e0402535a04206530653336313361666165383430376434376164323139633737633036323737020300d670040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26468cccf0008906e3b558e690000006e02004fb2535a17f81970b7234ff51&amp;ef=2&amp;bizid=1022" aeskey= "bd41fb4edb0943d19eadf4fcab993503" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=66c06eba96bf8c5305a327e71c1ad134&amp;filekey=3043020101042f302d02016e0402535a04203636633036656261393662663863353330356133323765373163316164313334020258a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26468cccf0009373f3b558e690000006e03004fb3535a17f81970b7234ff76&amp;ef=3&amp;bizid=1022" externmd5 = "62109334d133ac48e3ccc9017f1cb263" width= "636" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411673, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_/pEcS+wh|v1_JM8QmbwL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8559728647773071356, 'MsgSeq': 871430427}
2025-08-06 00:34:24 | INFO | 收到表情消息: 消息ID:1508762972 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:76e3a504110290e7f7bea10597d5096f 大小:54886
2025-08-06 00:34:24 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8559728647773071356
2025-08-06 00:34:33 | DEBUG | 收到消息: {'MsgId': 199141639, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_3156oxt19n0x22:\n<msg><emoji fromusername="wxid_3156oxt19n0x22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="a2cafd63adb50c8c4cbfd671a6065aaa" len="137943" productid="com.tencent.xin.emoticon.person.stiker_169124906042b03989f89a6b37" androidmd5="a2cafd63adb50c8c4cbfd671a6065aaa" androidlen="137943" s60v3md5="a2cafd63adb50c8c4cbfd671a6065aaa" s60v3len="137943" s60v5md5="a2cafd63adb50c8c4cbfd671a6065aaa" s60v5len="137943" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=a2cafd63adb50c8c4cbfd671a6065aaa&amp;filekey=30350201010421301f020201060402535a0410a2cafd63adb50c8c4cbfd671a6065aaa0203021ad7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264c8ab350000e48e07f6952b0000010600004f50535a2326ebc1e7497af64&amp;bizid=1023" designerid="" thumburl="http://wxapp.tc.qq.com/275/20304/stodownload?m=422469272a9494c296db46298ad2a853&amp;filekey=30340201010420301e02020113040253480410422469272a9494c296db46298ad2a85302025dbc040d00000004627466730000000132&amp;hy=SH&amp;storeid=264ce6e000007f365b477b1ce0000011300004f5053481a186bc1e6585b5d0&amp;bizid=1023" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=fffb6a60d84810493901332a4dfd8b14&amp;filekey=30350201010421301f020201060402535a0410fffb6a60d84810493901332a4dfd8b140203021ae0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264c8ab350002a8ef07f6952b0000010600004f50535a158998e0b731ac87e&amp;bizid=1023" aeskey="1cfc89708b82fe07f8b9ff64228211b6" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=d2fe42d2a8f5188500c5f454c53fe88d&amp;filekey=30350201010421301f020201060402535a0410d2fe42d2a8f5188500c5f454c53fe88d0203009010040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264c8ab35000aca6607f6952b0000010600004f50535a21766bc1e6514032c&amp;bizid=1023" externmd5="2e497e358bea4ca90d965f8a13cccf8c" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChEKB2RlZmF1bHQSBuWXt+WRnA=="></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411685, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_I0MRDM4F|v1_4KbJ0OfV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6570909771453746062, 'MsgSeq': 871430428}
2025-08-06 00:34:33 | INFO | 收到表情消息: 消息ID:199141639 来自:27852221909@chatroom 发送人:wxid_3156oxt19n0x22 MD5:a2cafd63adb50c8c4cbfd671a6065aaa 大小:137943
2025-08-06 00:34:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6570909771453746062
2025-08-06 00:34:35 | DEBUG | 收到消息: {'MsgId': 1209292609, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@帅\u2005拉出去打五十大板'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411688, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ubbh6q832tcs21]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_R48ZG51U|v1_Qp2uLEB9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7195207106402581724, 'MsgSeq': 871430429}
2025-08-06 00:34:35 | INFO | 收到文本消息: 消息ID:1209292609 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_ubbh6q832tcs21'] 内容:@帅 拉出去打五十大板
2025-08-06 00:34:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@帅 拉出去打五十大板' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:34:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['@帅\u2005拉出去打五十大板']
2025-08-06 00:34:35 | DEBUG | 处理消息内容: '@帅 拉出去打五十大板'
2025-08-06 00:34:35 | DEBUG | 消息内容 '@帅 拉出去打五十大板' 不匹配任何命令，忽略
2025-08-06 00:34:57 | DEBUG | 收到消息: {'MsgId': 490033801, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_zbh5p28da1si22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="cd20674f5ecba74e59dadea2ee234b02" encryver="1" cdnthumbaeskey="cd20674f5ecba74e59dadea2ee234b02" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b702045c6632700204689232bd042430643462326438652d373434372d343364352d393130662d366535313535653338353364020405290a020201000405004c4cd300" cdnthumblength="4101" cdnthumbheight="64" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204acc2e5ef02032f58b702045c6632700204689232bd042430643462326438652d373434372d343364352d393130662d366535313535653338353364020405290a020201000405004c4cd300" length="89380" md5="74339dd8d70d01956f0b3a931b7d1cce" hevc_mid_size="89380" originsourcemd5="9e9f5694a27ac895672cc18fc8f0ca05">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUxMTAwMDQwMTAwMDUwMDAiLCJwZHFIYXNoIjoiZjQzZWE1NWUwZjBiMTU0YTBm\nMGIxNTRlMGYwYjBjY2IwZjBiYzgwYjc1N2VmYWEwZjA3ZWJhZjRmMDdjMTU1NSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411709, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>fa046c18f0aadc52431abc5a46057b21_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_GBJaZutS|v1_L1ljLi0A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1613108887064630006, 'MsgSeq': 871430430}
2025-08-06 00:34:57 | INFO | 收到图片消息: 消息ID:490033801 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 XML:<?xml version="1.0"?><msg><img aeskey="cd20674f5ecba74e59dadea2ee234b02" encryver="1" cdnthumbaeskey="cd20674f5ecba74e59dadea2ee234b02" cdnthumburl="3057020100044b30490201000204acc2e5ef02032f58b702045c6632700204689232bd042430643462326438652d373434372d343364352d393130662d366535313535653338353364020405290a020201000405004c4cd300" cdnthumblength="4101" cdnthumbheight="64" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204acc2e5ef02032f58b702045c6632700204689232bd042430643462326438652d373434372d343364352d393130662d366535313535653338353364020405290a020201000405004c4cd300" length="89380" md5="74339dd8d70d01956f0b3a931b7d1cce" hevc_mid_size="89380" originsourcemd5="9e9f5694a27ac895672cc18fc8f0ca05"><secHashInfoBase64>eyJwaGFzaCI6IjUxMTAwMDQwMTAwMDUwMDAiLCJwZHFIYXNoIjoiZjQzZWE1NWUwZjBiMTU0YTBmMGIxNTRlMGYwYjBjY2IwZjBiYzgwYjc1N2VmYWEwZjA3ZWJhZjRmMDdjMTU1NSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-06 00:34:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-06 00:34:57 | INFO | [TimerTask] 缓存图片消息: 490033801
2025-08-06 00:35:03 | DEBUG | 收到消息: {'MsgId': 1022324163, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@QvemiY¹_慕ؓ悦ؓ˒\u2005你加哪里去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411715, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_2530z9t0joek22]]></atuserlist>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>15</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_LnliJnR4|v1_M/21oa3q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1544962447586460515, 'MsgSeq': 871430431}
2025-08-06 00:35:03 | INFO | 收到文本消息: 消息ID:1022324163 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_2530z9t0joek22'] 内容:@QvemiY¹_慕ؓ悦ؓ˒ 你加哪里去
2025-08-06 00:35:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@QvemiY¹_慕ؓ悦ؓ˒ 你加哪里去' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-06 00:35:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['@QvemiY¹_慕ؓ悦ؓ˒\u2005你加哪里去']
2025-08-06 00:35:03 | DEBUG | 处理消息内容: '@QvemiY¹_慕ؓ悦ؓ˒ 你加哪里去'
2025-08-06 00:35:03 | DEBUG | 消息内容 '@QvemiY¹_慕ؓ悦ؓ˒ 你加哪里去' 不匹配任何命令，忽略
2025-08-06 00:35:45 | DEBUG | 收到消息: {'MsgId': 1587501990, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="74346f8eb8e7f3ddd4fc71588eb29b6e" encryver="1" cdnthumbaeskey="74346f8eb8e7f3ddd4fc71588eb29b6e" cdnthumburl="3057020100044b30490201000204f060aea202032dce2d020491c5c2010204689232ed042434656334346163342d363937392d343833302d383435392d653531646536616534623333020405250a020201000405004c55cd00" cdnthumblength="4651" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032dce2d020491c5c2010204689232ed042434656334346163342d363937392d343833302d383435392d653531646536616534623333020405250a020201000405004c55cd00" length="1038511" md5="ee9d922e34a93b6384417765b151dbce" hevc_mid_size="95753" originsourcemd5="2697fae897b07d8e8383674de66f9761">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImYzMjAzMDEwNTAxMDUwMDAiLCJwZHFIYXNoIjoiZDBmMDM3MTc2NWE2ZDJlOTBm\nMmJkYWNiMDk0YjBkNTYyZDE2OGI0YjJlOTZlOTJiMTc5ZTU3YTk1NmFiMTIyOSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411757, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>e339e5a24c6ba83985d83d2dcfef4a6f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_IYuUpPhe|v1_phzuiwCy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5828477582336477975, 'MsgSeq': *********}
2025-08-06 00:35:45 | INFO | 收到图片消息: 消息ID:1587501990 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="74346f8eb8e7f3ddd4fc71588eb29b6e" encryver="1" cdnthumbaeskey="74346f8eb8e7f3ddd4fc71588eb29b6e" cdnthumburl="3057020100044b30490201000204f060aea202032dce2d020491c5c2010204689232ed042434656334346163342d363937392d343833302d383435392d653531646536616534623333020405250a020201000405004c55cd00" cdnthumblength="4651" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032dce2d020491c5c2010204689232ed042434656334346163342d363937392d343833302d383435392d653531646536616534623333020405250a020201000405004c55cd00" length="1038511" md5="ee9d922e34a93b6384417765b151dbce" hevc_mid_size="95753" originsourcemd5="2697fae897b07d8e8383674de66f9761"><secHashInfoBase64>eyJwaGFzaCI6ImYzMjAzMDEwNTAxMDUwMDAiLCJwZHFIYXNoIjoiZDBmMDM3MTc2NWE2ZDJlOTBmMmJkYWNiMDk0YjBkNTYyZDE2OGI0YjJlOTZlOTJiMTc5ZTU3YTk1NmFiMTIyOSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-06 00:35:45 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-06 00:35:45 | INFO | [TimerTask] 缓存图片消息: 1587501990
2025-08-06 00:35:54 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@赵如初\u2005你走错了吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411766, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_zbh5p28da1si22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Pdv73lqz|v1_R8e0ji9H</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5094959417013622619, 'MsgSeq': 871430433}
2025-08-06 00:35:54 | INFO | 收到文本消息: 消息ID:********* 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_zbh5p28da1si22'] 内容:@赵如初 你走错了吧
2025-08-06 00:35:54 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@赵如初 你走错了吧' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:35:54 | DEBUG | [DouBaoImageToImage] 命令解析: ['@赵如初\u2005你走错了吧']
2025-08-06 00:35:54 | DEBUG | 处理消息内容: '@赵如初 你走错了吧'
2025-08-06 00:35:54 | DEBUG | 消息内容 '@赵如初 你走错了吧' 不匹配任何命令，忽略
2025-08-06 00:36:03 | DEBUG | 收到消息: {'MsgId': 810922633, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n狮子3'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411775, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_octxQ2Fi|v1_P3NTUwkZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1855101622562579672, 'MsgSeq': 871430434}
2025-08-06 00:36:03 | INFO | 收到文本消息: 消息ID:810922633 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:狮子3
2025-08-06 00:36:03 | DEBUG | [DouBaoImageToImage] 收到文本消息: '狮子3' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:36:03 | DEBUG | [DouBaoImageToImage] 命令解析: ['狮子3']
2025-08-06 00:36:03 | DEBUG | 处理消息内容: '狮子3'
2025-08-06 00:36:03 | DEBUG | 消息内容 '狮子3' 不匹配任何命令，忽略
2025-08-06 00:36:26 | DEBUG | 收到消息: {'MsgId': 7265034, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n在了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411798, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gQxTyAVd|v1_cW1IxE/w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9183469104273152781, 'MsgSeq': 871430435}
2025-08-06 00:36:26 | INFO | 收到文本消息: 消息ID:7265034 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:在了
2025-08-06 00:36:26 | DEBUG | [DouBaoImageToImage] 收到文本消息: '在了' from wxid_zbh5p28da1si22 in 27852221909@chatroom
2025-08-06 00:36:26 | DEBUG | [DouBaoImageToImage] 命令解析: ['在了']
2025-08-06 00:36:26 | DEBUG | 处理消息内容: '在了'
2025-08-06 00:36:26 | DEBUG | 消息内容 '在了' 不匹配任何命令，忽略
2025-08-06 00:38:46 | DEBUG | 收到消息: {'MsgId': 1300227154, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fjgby1dzvif021:\n点歌 丑八怪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411938, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_icuIkfQh|v1_cWGNkQfD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '￼ : 点歌 丑八怪', 'NewMsgId': 3825124401684464505, 'MsgSeq': 871430436}
2025-08-06 00:38:46 | INFO | 收到文本消息: 消息ID:1300227154 来自:47325400669@chatroom 发送人:wxid_fjgby1dzvif021 @:[] 内容:点歌 丑八怪
2025-08-06 00:38:46 | DEBUG | [DouBaoImageToImage] 收到文本消息: '点歌 丑八怪' from wxid_fjgby1dzvif021 in 47325400669@chatroom
2025-08-06 00:38:46 | DEBUG | [DouBaoImageToImage] 命令解析: ['点歌', '丑八怪']
2025-08-06 00:38:49 | INFO | 发送app消息: 对方wxid:47325400669@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>丑八怪</title><des>薛之谦</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://er-sycdn.kuwo.cn/c4775efcd09a48af59a718e331cf9c37/689233a5/resource/1307392909/trackmedia/F000001owvQ04gVgSM_1.flac?src=无损音质 FLAC_23.66 MB?src=flac_23.66 MB?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://er-sycdn.kuwo.cn/c4775efcd09a48af59a718e331cf9c37/689233a5/resource/1307392909/trackmedia/F000001owvQ04gVgSM_1.flac?src=无损音质 FLAC_23.66 MB?src=flac_23.66 MB?from=longzhu_api</lowdataurl><recorditem/><thumburl>http://imge.kugou.com/stdmusic/400/20200407/20200407151818862082.jpg</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>﻿[id:$00000000]
[ar:薛之谦]
[ti:丑八怪 (《如果我爱你》电视剧插曲)]
[by:]
[hash:7f90c597a51614d1874b5a12c32c84a0]
[al:意外]
[sign:]
[qq:]
[total:0]
[offset:0]
[00:00.00]薛之谦 - 丑八怪
[00:00.66]作词：甘世佳
[00:01.78]作曲：李荣浩
[00:11.80]编曲：李荣浩
[00:15.74]制作人：李荣浩
[00:19.67]如果世界漆黑 其实我很美
[00:23.64]在爱情里面进退 最多被消费
[00:27.47]无关痛痒的是非
[00:29.24]又怎么不对 无所谓
[00:35.18]如果像你一样 总有人赞美
[00:38.95]围绕着我的卑微 也许能消退
[00:42.91]其实我并不在意 有很多机会
[00:46.34]像巨人一样的无畏
[00:49.15]放纵我 心里的鬼
[00:50.97]可是我不配
[00:54.02]丑八怪 能否别把灯打开
[01:01.65]我要的爱 出没在
[01:06.29]漆黑一片的舞台
[01:09.24]丑八怪 在这暧昧的时代
[01:17.22]我的存在 像意外
[01:37.26]有人用一滴泪 会红颜祸水
[01:41.08]有人丢掉称谓 什么也不会
[01:44.75]只要你足够虚伪 就不怕魔鬼
[01:48.52]对不对
[01:52.75]如果剧本写好 谁比谁高贵
[01:56.32]我只能沉默以对 美丽本无罪
[02:00.10]当欲望开始贪杯 有更多机会
[02:03.82]像尘埃一样的无畏 化成灰
[02:07.22]谁认得谁
[02:08.37]管他配不配
[02:11.44]丑八怪 能否别把灯打开
[02:19.28]我要的爱 出没在
[02:23.68]漆黑一片的舞台
[02:26.62]丑八怪 在这暧昧的时代
[02:34.64]我的存在 不意外
[03:01.41]丑八怪 其实见多就不怪
[03:09.53]放肆去high 用力踩
[03:13.83]那不堪一击的洁白
[03:17.03]丑八怪 这是我们的时代
[03:24.93]我不存在 才意外
</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://imge.kugou.com/stdmusic/400/20200407/20200407151818862082.jpg</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-08-06 00:38:49 | DEBUG | 处理消息内容: '点歌 丑八怪'
2025-08-06 00:38:49 | DEBUG | 消息内容 '点歌 丑八怪' 不匹配任何命令，忽略
2025-08-06 00:38:53 | DEBUG | 收到消息: {'MsgId': 440862940, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'q363059309:\n<msg>\n\t<appmsg appid="wx76fdd06dde311af3" sdkver="0">\n\t\t<title>丑八怪</title>\n\t\t<des>薛之谦</des>\n\t\t<type>3</type>\n\t\t<url>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</url>\n\t\t<lowurl>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</lowurl>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</dataurl>\n\t\t<lowdataurl>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</lowdataurl>\n\t\t<songalbumurl />\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>q363059309</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>132</version>\n\t\t<appname>抖音短视频</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411944, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>f1675ad260a1b01f8a5c085521ada791_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_G/FJ91X2|v1_43mvngKW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 1417083212045749375, 'MsgSeq': 871430439}
2025-08-06 00:38:53 | DEBUG | 从群聊消息中提取发送者: q363059309
2025-08-06 00:38:53 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="wx76fdd06dde311af3" sdkver="0">
		<title>丑八怪</title>
		<des>薛之谦</des>
		<type>3</type>
		<url>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</url>
		<lowurl>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</lowurl>
		<dataurl>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</dataurl>
		<lowdataurl>https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3</lowdataurl>
		<songalbumurl />
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>q363059309</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>132</version>
		<appname>抖音短视频</appname>
	</appinfo>
	<commenturl />
</msg>

2025-08-06 00:38:53 | DEBUG | XML消息类型: 3
2025-08-06 00:38:53 | DEBUG | XML消息标题: 丑八怪
2025-08-06 00:38:53 | DEBUG | XML消息描述: 薛之谦
2025-08-06 00:38:53 | DEBUG | XML消息URL: https://er-sycdn.kuwo.cn/864f8eb89aa0002fb2ec0157cb016d64/689233a7/resource/30106/trackmedia/M800001owvQ04gVgSM.mp3
2025-08-06 00:38:53 | INFO | 收到红包消息: 标题:丑八怪 描述:薛之谦 来自:47325400669@chatroom 发送人:wxid_4usgcju5ey9q29
2025-08-06 00:38:53 | DEBUG | 收到消息: {'MsgId': 2046606012, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1jjimgid98no12:\n@￼\u2005\n🎶----- 找到以下歌曲 -----🎶\n1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤\n2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤\n3. 🎵 丑八怪 + 演员 - 薛之谦 🎤\n4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤\n5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤\n6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤\n7. 🎵 丑八怪 (Live) - 陆星材 🎤\n_________________________\n🎵输入 “播放 + 序号” 播放歌曲🎵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411945, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_fjgby1dzvif021</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_Xf9Jid1n|v1_B363AVUf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ting : @￼\u2005\n\ue326----- 找到以下歌曲 -----\ue326\n1. \ue03e 柳州DJ晨七七-丑八怪《DJ版...', 'NewMsgId': 8615449061555835398, 'MsgSeq': 871430440}
2025-08-06 00:38:53 | INFO | 收到文本消息: 消息ID:2046606012 来自:47325400669@chatroom 发送人:wxid_1jjimgid98no12 @:['wxid_fjgby1dzvif021'] 内容:@￼ 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤
2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤
3. 🎵 丑八怪 + 演员 - 薛之谦 🎤
4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤
5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤
6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤
7. 🎵 丑八怪 (Live) - 陆星材 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵
2025-08-06 00:38:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@￼ 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤
2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤
3. 🎵 丑八怪 + 演员 - 薛之谦 🎤
4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤
5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤
6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤
7. 🎵 丑八怪 (Live) - 陆星材 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵' from wxid_1jjimgid98no12 in 47325400669@chatroom
2025-08-06 00:38:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['@￼\u2005\n🎶-----', '找到以下歌曲', '-----🎶\n1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤\n2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤\n3. 🎵 丑八怪 + 演员 - 薛之谦 🎤\n4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤\n5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤\n6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤\n7. 🎵 丑八怪 (Live) - 陆星材 🎤\n_________________________\n🎵输入 “播放 + 序号” 播放歌曲🎵']
2025-08-06 00:38:53 | DEBUG | 处理消息内容: '@￼ 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤
2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤
3. 🎵 丑八怪 + 演员 - 薛之谦 🎤
4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤
5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤
6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤
7. 🎵 丑八怪 (Live) - 陆星材 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵'
2025-08-06 00:38:53 | DEBUG | 消息内容 '@￼ 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤
2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤
3. 🎵 丑八怪 + 演员 - 薛之谦 🎤
4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤
5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤
6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤
7. 🎵 丑八怪 (Live) - 陆星材 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵' 不匹配任何命令，忽略
2025-08-06 00:38:58 | DEBUG | 收到消息: {'MsgId': 95980486, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>1417083212045749375</newmsgid>\n    <sec_msg_node>\n      <sfn>0</sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len>0</clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[0]]></fold-reduce>\n      <block-range>1</block-range>\n      <media-to-emoji>0</media-to-emoji>\n      <bubble-type>1</bubble-type>\n      <preview-type>0</preview-type>\n      <url-click-type>0</url-click-type>\n      <sec-ctrl-flag>0</sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag>0</risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411945, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8871321680096005124, 'MsgSeq': 871430441}
2025-08-06 00:38:58 | DEBUG | 系统消息类型: secmsg
2025-08-06 00:38:58 | INFO | 未知的系统消息类型: {'MsgId': 95980486, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>1417083212045749375</newmsgid>\n    <sec_msg_node>\n      <sfn>0</sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len>0</clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[0]]></fold-reduce>\n      <block-range>1</block-range>\n      <media-to-emoji>0</media-to-emoji>\n      <bubble-type>1</bubble-type>\n      <preview-type>0</preview-type>\n      <url-click-type>0</url-click-type>\n      <sec-ctrl-flag>0</sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag>0</risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411945, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8871321680096005124, 'MsgSeq': 871430441, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-08-06 00:38:58 | DEBUG | 收到消息: {'MsgId': 1297392231, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>群聊的聊天记录</title>\n\t\t<des>阿猪米德: 🎵 歌曲列表 🎵\n\n 1. 《丑八怪》 - 薛之谦\n 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦\n 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然\n 4. 《丑八怪 (Live)》 - 薛之谦\n 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥\n 6. 《丑八怪 (DJ版)》 - 薛之谦\n 7. 《丑八怪 (Live)》 - 李克勤\n 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆\n 9. 《丑八怪 (Live)》 - 巴图\n10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维\n11. 《丑八怪 (DJ 阿树版)》 - 薛之谦\n12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦\n13. 《丑八怪 (Demo)》 - 陈宇威\n14. 《丑八怪 (15秒DJ版片段)》 - 于77\n15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo\n16. 《丑八怪》 - Fancy\n17. 《丑八怪 (DJ版)》 - DJ\n18. 《丑八怪》 - Lee\n19. 《丑八怪 (DJ版)》 - 幸怀蝶\n20. 《丑八怪 (Live)》 - 华语群星\n\n💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</des>\n\t\t<action />\n\t\t<type>19</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<recorditem><![CDATA[<recordinfo><fromscene>0</fromscene><favcreatetime>1754411945</favcreatetime><isChatRoom>0</isChatRoom><title>群聊的聊天记录</title><desc>阿猪米德: 🎵 歌曲列表 🎵\n\n 1. 《丑八怪》 - 薛之谦\n 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦\n 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然\n 4. 《丑八怪 (Live)》 - 薛之谦\n 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥\n 6. 《丑八怪 (DJ版)》 - 薛之谦\n 7. 《丑八怪 (Live)》 - 李克勤\n 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆\n 9. 《丑八怪 (Live)》 - 巴图\n10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维\n11. 《丑八怪 (DJ 阿树版)》 - 薛之谦\n12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦\n13. 《丑八怪 (Demo)》 - 陈宇威\n14. 《丑八怪 (15秒DJ版片段)》 - 于77\n15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo\n16. 《丑八怪》 - Fancy\n17. 《丑八怪 (DJ版)》 - DJ\n18. 《丑八怪》 - Lee\n19. 《丑八怪 (DJ版)》 - 幸怀蝶\n20. 《丑八怪 (Live)》 - 华语群星\n\n💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</desc><datalist count="1"><dataitem datatype="1" dataid="8a95fc81eea3cd4b19da72c711ca6a9f" htmlid="8a95fc81eea3cd4b19da72c711ca6a9f"><sourcename>阿猪米德</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/FwBha00mTQXVxz0EjZy3g8CeicEAIujXyGHVE9Qialib8EH86Fz1neUeZbs3Qp757tIestENic7oeQ9s36X7rjQo8INFvJ4qD554TyeibtUgCalibSiaReThicr1ux0Gb0QlPsQv/132</sourceheadurl><sourcetime>2025-08-06 00:39:05</sourcetime><datadesc>🎵 歌曲列表 🎵\n\n 1. 《丑八怪》 - 薛之谦\n 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦\n 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然\n 4. 《丑八怪 (Live)》 - 薛之谦\n 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥\n 6. 《丑八怪 (DJ版)》 - 薛之谦\n 7. 《丑八怪 (Live)》 - 李克勤\n 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆\n 9. 《丑八怪 (Live)》 - 巴图\n10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维\n11. 《丑八怪 (DJ 阿树版)》 - 薛之谦\n12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦\n13. 《丑八怪 (Demo)》 - 陈宇威\n14. 《丑八怪 (15秒DJ版片段)》 - 于77\n15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo\n16. 《丑八怪》 - Fancy\n17. 《丑八怪 (DJ版)》 - DJ\n18. 《丑八怪》 - Lee\n19. 《丑八怪 (DJ版)》 - 幸怀蝶\n20. 《丑八怪 (Live)》 - 华语群星\n\n💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</datadesc><srcMsgLocalid>1754411945</srcMsgLocalid><srcMsgCreateTime>1754411945</srcMsgCreateTime><fromnewmsgid>1754411945000</fromnewmsgid><dataitemsource><hashusername>24d1e77dc3af35ffd7dfc00d46657cbdb61050e84b921d2feff80f7cc1eb936e</hashusername></dataitemsource></dataitem></datalist></recordinfo>]]></recorditem>\n\t</appmsg>\n\t<fromusername>wxid_fh84okl6f5wp22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411950, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<passthrough>\n\t\t<forward_depth>0</forward_depth>\n\t</passthrough>\n\t<sec_msg_node>\n\t\t<uuid>33df80bdbbc80731c8be7362fe1a05ec_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_C+l1CzWG|v1_LfLGtyrQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : [聊天记录]', 'NewMsgId': 5959583934039747198, 'MsgSeq': 871430442}
2025-08-06 00:38:58 | DEBUG | 从群聊消息中提取发送者: wxid_fh84okl6f5wp22
2025-08-06 00:38:58 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>群聊的聊天记录</title>
		<des>阿猪米德: 🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</des>
		<action />
		<type>19</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5></emoticonmd5>
			<fileext />
			<aeskey></aeskey>
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl />
		<md5 />
		<statextstr />
		<recorditem><![CDATA[<recordinfo><fromscene>0</fromscene><favcreatetime>1754411945</favcreatetime><isChatRoom>0</isChatRoom><title>群聊的聊天记录</title><desc>阿猪米德: 🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</desc><datalist count="1"><dataitem datatype="1" dataid="8a95fc81eea3cd4b19da72c711ca6a9f" htmlid="8a95fc81eea3cd4b19da72c711ca6a9f"><sourcename>阿猪米德</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/FwBha00mTQXVxz0EjZy3g8CeicEAIujXyGHVE9Qialib8EH86Fz1neUeZbs3Qp757tIestENic7oeQ9s36X7rjQo8INFvJ4qD554TyeibtUgCalibSiaReThicr1ux0Gb0QlPsQv/132</sourceheadurl><sourcetime>2025-08-06 00:39:05</sourcetime><datadesc>🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</datadesc><srcMsgLocalid>1754411945</srcMsgLocalid><srcMsgCreateTime>1754411945</srcMsgCreateTime><fromnewmsgid>1754411945000</fromnewmsgid><dataitemsource><hashusername>24d1e77dc3af35ffd7dfc00d46657cbdb61050e84b921d2feff80f7cc1eb936e</hashusername></dataitemsource></dataitem></datalist></recordinfo>]]></recorditem>
	</appmsg>
	<fromusername>wxid_fh84okl6f5wp22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-06 00:38:58 | DEBUG | XML消息类型: 19
2025-08-06 00:38:58 | DEBUG | XML消息标题: 群聊的聊天记录
2025-08-06 00:38:58 | DEBUG | XML消息描述: 阿猪米德: 🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"
2025-08-06 00:38:58 | DEBUG | 附件信息 totallen: 0
2025-08-06 00:38:58 | INFO | 未知的XML消息类型: 19
2025-08-06 00:38:58 | INFO | 消息标题: 群聊的聊天记录
2025-08-06 00:38:58 | INFO | 消息描述: 阿猪米德: 🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"
2025-08-06 00:38:58 | INFO | 消息URL: N/A
2025-08-06 00:38:58 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>群聊的聊天记录</title>
		<des>阿猪米德: 🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</des>
		<action />
		<type>19</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5></emoticonmd5>
			<fileext />
			<aeskey></aeskey>
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl />
		<md5 />
		<statextstr />
		<recorditem><![CDATA[<recordinfo><fromscene>0</fromscene><favcreatetime>1754411945</favcreatetime><isChatRoom>0</isChatRoom><title>群聊的聊天记录</title><desc>阿猪米德: 🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</desc><datalist count="1"><dataitem datatype="1" dataid="8a95fc81eea3cd4b19da72c711ca6a9f" htmlid="8a95fc81eea3cd4b19da72c711ca6a9f"><sourcename>阿猪米德</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/FwBha00mTQXVxz0EjZy3g8CeicEAIujXyGHVE9Qialib8EH86Fz1neUeZbs3Qp757tIestENic7oeQ9s36X7rjQo8INFvJ4qD554TyeibtUgCalibSiaReThicr1ux0Gb0QlPsQv/132</sourceheadurl><sourcetime>2025-08-06 00:39:05</sourcetime><datadesc>🎵 歌曲列表 🎵

 1. 《丑八怪》 - 薛之谦
 2. 《丑八怪 (DJ 阿若版)》 - 薛之谦
 3. 《丑八怪 (Live)》 - 薛之谦/张婉清/黄云龙/童英然
 4. 《丑八怪 (Live)》 - 薛之谦
 5. 《丑八怪 (Live)》 - 黄雅莉/李祥祥
 6. 《丑八怪 (DJ版)》 - 薛之谦
 7. 《丑八怪 (Live)》 - 李克勤
 8. 《丑八怪 (Live)》 - 薛之谦/沛子歆
 9. 《丑八怪 (Live)》 - 巴图
10. 《丑八怪 (Live)》 - 李克勤/郭静/金池/袁成杰/刘维
11. 《丑八怪 (DJ 阿树版)》 - 薛之谦
12. 《丑八怪 (34秒2020江苏卫视55青春选择之夜晚会现场片段|2020江苏卫视55青春选择之夜晚会现场片段)》 - 薛之谦
13. 《丑八怪 (Demo)》 - 陈宇威
14. 《丑八怪 (15秒DJ版片段)》 - 于77
15. 《丑八怪 (DJ阿lo版)》 - 薛之谦/DJ阿lo
16. 《丑八怪》 - Fancy
17. 《丑八怪 (DJ版)》 - DJ
18. 《丑八怪》 - Lee
19. 《丑八怪 (DJ版)》 - 幸怀蝶
20. 《丑八怪 (Live)》 - 华语群星

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</datadesc><srcMsgLocalid>1754411945</srcMsgLocalid><srcMsgCreateTime>1754411945</srcMsgCreateTime><fromnewmsgid>1754411945000</fromnewmsgid><dataitemsource><hashusername>24d1e77dc3af35ffd7dfc00d46657cbdb61050e84b921d2feff80f7cc1eb936e</hashusername></dataitemsource></dataitem></datalist></recordinfo>]]></recorditem>
	</appmsg>
	<fromusername>wxid_fh84okl6f5wp22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-06 00:39:24 | DEBUG | 收到消息: {'MsgId': 810402416, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_fjgby1dzvif021:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你用的啥接口，我这很多时候都没有原唱…@郭\u2005</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8615449061555835398</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_1jjimgid98no12</chatusr>\n\t\t\t<displayname>ting</displayname>\n\t\t\t<content>@￼\u2005\n🎶----- 找到以下歌曲 -----🎶\n1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤\n2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤\n3. 🎵 丑八怪 + 演员 - 薛之谦 🎤\n4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤\n5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤\n6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤\n7. 🎵 丑八怪 (Live) - 陆星材 🎤\n_________________________\n🎵输入 “播放 + 序号” 播放歌曲🎵</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;820106005&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;wxid_fjgby1dzvif021&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;224&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_XyORFIP+|v1_UoxOPFUi&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754411945</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_fjgby1dzvif021</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754411976, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ubbh6q832tcs21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>26f5b3045290f6e809340824b3ae250b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_dZZ7IfgS|v1_sY+QvIbj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '￼ : 你用的啥接口，我这很多时候都没有原唱…@郭\u2005', 'NewMsgId': 87448136439792338, 'MsgSeq': 871430443}
2025-08-06 00:39:24 | DEBUG | 从群聊消息中提取发送者: wxid_fjgby1dzvif021
2025-08-06 00:39:24 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 00:39:24 | INFO | 收到引用消息: 消息ID:810402416 来自:47325400669@chatroom 发送人:wxid_fjgby1dzvif021 内容:你用的啥接口，我这很多时候都没有原唱…@郭  引用类型:1
2025-08-06 00:39:24 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 00:39:24 | INFO | [DouBaoImageToImage] 消息内容: '你用的啥接口，我这很多时候都没有原唱…@郭' from wxid_fjgby1dzvif021 in 47325400669@chatroom
2025-08-06 00:39:24 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['你用的啥接口，我这很多时候都没有原唱…@郭']
2025-08-06 00:39:24 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 00:39:24 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 00:39:24 | INFO |   - 消息内容: 你用的啥接口，我这很多时候都没有原唱…@郭
2025-08-06 00:39:24 | INFO |   - 群组ID: 47325400669@chatroom
2025-08-06 00:39:24 | INFO |   - 发送人: wxid_fjgby1dzvif021
2025-08-06 00:39:24 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@￼\u2005\n🎶----- 找到以下歌曲 -----🎶\n1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤\n2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤\n3. 🎵 丑八怪 + 演员 - 薛之谦 🎤\n4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤\n5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤\n6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤\n7. 🎵 丑八怪 (Live) - 陆星材 🎤\n_________________________\n🎵输入 “播放 + 序号” 播放歌曲🎵', 'Msgid': '8615449061555835398', 'NewMsgId': '8615449061555835398', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': 'ting', 'MsgSource': '<msgsource><sequence_id>820106005</sequence_id>\n\t<atuserlist>wxid_fjgby1dzvif021</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>1</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_XyORFIP+|v1_UoxOPFUi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754411945', 'SenderWxid': 'wxid_fjgby1dzvif021'}
2025-08-06 00:39:24 | INFO |   - 引用消息ID: 
2025-08-06 00:39:24 | INFO |   - 引用消息类型: 
2025-08-06 00:39:24 | INFO |   - 引用消息内容: @￼ 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 柳州DJ晨七七-丑八怪《DJ版》 - 柳州DJ晨七七 🎤
2. 🎵 丑八怪（Cover 薛之谦） - 随风 🎤
3. 🎵 丑八怪 + 演员 - 薛之谦 🎤
4. 🎵 丑八怪（演唱会重制版） - 鱼木敲木鱼 🎤
5. 🎵 - 丑八怪（林音 remix） - 苏凡 🎤
6. 🎵 丑八怪 （DJ版） - 无法释怀 🎤
7. 🎵 丑八怪 (Live) - 陆星材 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵
2025-08-06 00:39:24 | INFO |   - 引用消息发送人: wxid_fjgby1dzvif021
2025-08-06 00:42:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 00:46:21 | DEBUG | 收到消息: {'MsgId': 1687821779, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="02f9b83ca472bdd79172c7b13f5e9741" encryver="1" cdnthumbaeskey="02f9b83ca472bdd79172c7b13f5e9741" cdnthumburl="3057020100044b30490201000204f060aea202032dce2d020491c5c201020468923568042465376633363332652d343039312d343836642d623031312d613334643637343133366137020405250a020201000405004c4d9900" cdnthumblength="5028" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032dce2d020491c5c201020468923568042465376633363332652d343039312d343836642d623031312d613334643637343133366137020405250a020201000405004c4d9900" length="1202208" md5="fa25457c9fa1f984f100fa16ffbc7605" hevc_mid_size="122764" originsourcemd5="320ba01b307872c1dd37169d4ef55d81">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjcxMDA1MDAwNTAwMDEwMDAiLCJwZHFIYXNoIjoiZDRhOWFkNWIzNGI4YjA3MjBh\nNzFlMzljMjcxYzQ3YjQxZTFlNmEwY2IwYWZhYWUxN2M2YjFjNzFiZDRiYjEwZiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412392, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>e51111a447b2e449d60b1c2004dc5052_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_gDJI8UJp|v1_2hOuEDE8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7684153303691754640, 'MsgSeq': 871430444}
2025-08-06 00:46:21 | INFO | 收到图片消息: 消息ID:1687821779 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="02f9b83ca472bdd79172c7b13f5e9741" encryver="1" cdnthumbaeskey="02f9b83ca472bdd79172c7b13f5e9741" cdnthumburl="3057020100044b30490201000204f060aea202032dce2d020491c5c201020468923568042465376633363332652d343039312d343836642d623031312d613334643637343133366137020405250a020201000405004c4d9900" cdnthumblength="5028" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032dce2d020491c5c201020468923568042465376633363332652d343039312d343836642d623031312d613334643637343133366137020405250a020201000405004c4d9900" length="1202208" md5="fa25457c9fa1f984f100fa16ffbc7605" hevc_mid_size="122764" originsourcemd5="320ba01b307872c1dd37169d4ef55d81"><secHashInfoBase64>eyJwaGFzaCI6IjcxMDA1MDAwNTAwMDEwMDAiLCJwZHFIYXNoIjoiZDRhOWFkNWIzNGI4YjA3MjBhNzFlMzljMjcxYzQ3YjQxZTFlNmEwY2IwYWZhYWUxN2M2YjFjNzFiZDRiYjEwZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-06 00:46:21 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-06 00:46:21 | INFO | [TimerTask] 缓存图片消息: 1687821779
2025-08-06 00:46:32 | DEBUG | 收到消息: {'MsgId': 1065292572, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这个衣服有图鉴吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412404, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_qMj/HmDa|v1_z/97Jvqn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7124603772509576554, 'MsgSeq': 871430445}
2025-08-06 00:46:32 | INFO | 收到文本消息: 消息ID:1065292572 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这个衣服有图鉴吗
2025-08-06 00:46:32 | DEBUG | [DouBaoImageToImage] 收到文本消息: '这个衣服有图鉴吗' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:46:32 | DEBUG | [DouBaoImageToImage] 命令解析: ['这个衣服有图鉴吗']
2025-08-06 00:46:32 | DEBUG | 处理消息内容: '这个衣服有图鉴吗'
2025-08-06 00:46:32 | DEBUG | 消息内容 '这个衣服有图鉴吗' 不匹配任何命令，忽略
2025-08-06 00:48:45 | DEBUG | 收到消息: {'MsgId': 360022939, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n🈚️'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412537, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_kr81/KJb|v1_TxYIPQX4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4868204430579119334, 'MsgSeq': 871430446}
2025-08-06 00:48:45 | INFO | 收到文本消息: 消息ID:360022939 来自:27852221909@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:🈚️
2025-08-06 00:48:45 | DEBUG | [DouBaoImageToImage] 收到文本消息: '🈚️' from wxid_1ul5r40nibpn12 in 27852221909@chatroom
2025-08-06 00:48:45 | DEBUG | [DouBaoImageToImage] 命令解析: ['🈚️']
2025-08-06 00:48:45 | DEBUG | 处理消息内容: '🈚️'
2025-08-06 00:48:45 | DEBUG | 消息内容 '🈚️' 不匹配任何命令，忽略
2025-08-06 00:48:53 | DEBUG | 收到消息: {'MsgId': 2099203245, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n点进去看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412545, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_VsJKu3Im|v1_ZP31yZjQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 128556579050520332, 'MsgSeq': 871430447}
2025-08-06 00:48:53 | INFO | 收到文本消息: 消息ID:2099203245 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:点进去看
2025-08-06 00:48:53 | DEBUG | [DouBaoImageToImage] 收到文本消息: '点进去看' from wxid_bmzp9achod6922 in 27852221909@chatroom
2025-08-06 00:48:53 | DEBUG | [DouBaoImageToImage] 命令解析: ['点进去看']
2025-08-06 00:48:53 | DEBUG | 处理消息内容: '点进去看'
2025-08-06 00:48:53 | DEBUG | 消息内容 '点进去看' 不匹配任何命令，忽略
2025-08-06 00:54:21 | DEBUG | 收到消息: {'MsgId': 1617887079, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412873, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_PGDhf7SZ|v1_jbJQX2y4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 1715403513333608153, 'MsgSeq': 871430448}
2025-08-06 00:54:21 | INFO | 收到表情消息: 消息ID:1617887079 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-08-06 00:54:21 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1715403513333608153
2025-08-06 00:56:21 | DEBUG | 收到消息: {'MsgId': 1814052680, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n点歌 偏爱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412993, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_NgemqbE8|v1_7Ne/RJjJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 点歌 偏爱', 'NewMsgId': 6265169622338964234, 'MsgSeq': 871430449}
2025-08-06 00:56:21 | INFO | 收到文本消息: 消息ID:1814052680 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:点歌 偏爱
2025-08-06 00:56:21 | DEBUG | [DouBaoImageToImage] 收到文本消息: '点歌 偏爱' from wxid_ubbh6q832tcs21 in 47325400669@chatroom
2025-08-06 00:56:21 | DEBUG | [DouBaoImageToImage] 命令解析: ['点歌', '偏爱']
2025-08-06 00:56:24 | INFO | 发送app消息: 对方wxid:47325400669@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>偏爱</title><des>张芸京</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://lx-sycdn.kuwo.cn/7bd233d558b773e4f2631a2ed48cfd23/689237c4/resource/s1/98/82/4260546273.flac?src=无损音质 FLAC_24.19 MB?src=flac_24.19 MB?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://lx-sycdn.kuwo.cn/7bd233d558b773e4f2631a2ed48cfd23/689237c4/resource/s1/98/82/4260546273.flac?src=无损音质 FLAC_24.19 MB?src=flac_24.19 MB?from=longzhu_api</lowdataurl><recorditem/><thumburl>http://imge.kugou.com/stdmusic/400/20221125/20221125063514484979.jpg</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>﻿[id:$00000000]
[ar:张芸京]
[ti:偏爱 (《仙剑奇侠传3》电视剧插曲)]
[by:]
[hash:4a544525c1f78dbdf25361db0e255495]
[al:破天荒]
[sign:]
[qq:]
[total:0]
[offset:0]
[00:00.00]张芸京 - 偏爱
[00:03.95]词：葛大为
[00:07.90]曲：陈伟
[00:11.85]编曲：陈伟
[00:15.80]把昨天都作废现在你在我眼前
[00:23.03]我想爱请给我机会
[00:29.57]如果我错了也承担
[00:33.40]认定你就是答案
[00:38.16]我不怕谁嘲笑我极端
[00:43.26]相信自己的直觉
[00:47.19]顽固的人不喊累
[00:50.58]爱上你我不撤退
[00:56.10]我说过我不闪躲我非要这么做
[01:02.03]讲不听也偏要爱
[01:04.48]更努力爱让你明白
[01:09.78]没有别条路能走
[01:12.92]你决定要不要陪我
[01:15.89]讲不听偏爱看我感觉爱
[01:19.25]等你的依赖对你偏爱
[01:29.56]痛也很愉快
[01:38.06]把昨天都作废现在你在我眼前
[01:45.23]我想爱请给我机会
[01:51.79]如果我错了也承担
[01:55.69]认定你就是答案
[02:00.40]我不怕谁嘲笑我极端
[02:05.49]相信自己的直觉
[02:09.41]顽固的人不喊累
[02:12.80]爱上你我不撤退
[02:18.35]我说过我不闪躲我非要这么做
[02:24.44]讲不听也偏要爱
[02:26.79]更努力爱让你明白
[02:32.10]没有别条路能走
[02:35.18]你决定要不要陪我
[02:38.16]讲不听偏爱看我感觉爱
[02:41.55]等你的依赖
[02:44.59]不后悔有把握
[02:47.41]我不闪躲我非要这么做
[02:51.89]讲不听也偏要爱
[02:54.17]更努力爱让你明白
[02:59.49]没有别条路能走
[03:02.61]你决定要不要陪我
[03:05.57]讲不听偏爱看我感觉爱
[03:08.99]等你的依赖对你偏爱
[03:19.26]痛也很愉快
</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://imge.kugou.com/stdmusic/400/20221125/20221125063514484979.jpg</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-08-06 00:56:24 | DEBUG | 处理消息内容: '点歌 偏爱'
2025-08-06 00:56:24 | DEBUG | 消息内容 '点歌 偏爱' 不匹配任何命令，忽略
2025-08-06 00:56:25 | DEBUG | 收到消息: {'MsgId': 1011942934, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'q363059309:\n<msg>\n\t<appmsg appid="wx76fdd06dde311af3" sdkver="0">\n\t\t<title>偏爱</title>\n\t\t<des>张芸京</des>\n\t\t<type>3</type>\n\t\t<url>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</url>\n\t\t<lowurl>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</lowurl>\n\t\t<dataurl>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</dataurl>\n\t\t<lowdataurl>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</lowdataurl>\n\t\t<songalbumurl />\n\t\t<songlyric>[00:15.75]把昨天都作废现在你在我眼前[00:22.79]我想爱请给我机会[00:29.95]如果我错了也承担[00:33.31]认定你就是答案[00:37.99]我不怕谁嘲笑我极端[00:43.15]相信自己的直觉[00:46.99]顽固的人不喊累[00:50.43]爱上你我不撤退[00:56.03]我说过我不闪躲我非要这么做[01:02.03]讲不听也偏要爱[01:04.39]更努力爱让你明白[01:09.75]没有别条路能走[01:12.75]你决定要不要陪我[01:15.75]讲不听偏爱靠我感觉爱[01:19.15]等你的依赖对你偏爱[01:29.43]痛也很愉快[01:37.99]把昨天都作废现在你在我眼前[01:45.07]我想爱请给我机会[01:51.71]如果我错了也承担[01:55.55]认定你就是答案[02:00.31]我不怕谁嘲笑我极端[02:05.43]相信自己的直觉[02:09.27]顽固的人不喊累[02:12.75]爱上你我不撤退[02:18.31]我说过我不闪躲我非要这么做[02:24.35]讲不听也偏要爱[02:26.63]更努力爱让你明白[02:32.03]没有别条路能走[02:35.07]你决定要不要陪我[02:38.07]讲不听偏爱靠我感觉爱[02:41.47]等你的依赖[02:44.43]不后悔有把握[02:47.23]我不闪躲我非要这么做[02:51.71]讲不听也偏要爱[02:54.07]更努力爱让你明白[02:59.95]没有别条路能走[03:02.47]你决定要不要陪我[03:05.47]讲不听偏爱靠我感觉爱[03:08.87]等你的依赖对你偏爱[03:19.15]痛也很愉快</songlyric>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>q363059309</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>132</version>\n\t\t<appname>抖音短视频</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412997, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>05143ba6151a939af5d6d018c552ae56_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_ePX0498t|v1_MfEvhHxQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 4750203700754884609, 'MsgSeq': 871430452}
2025-08-06 00:56:25 | DEBUG | 从群聊消息中提取发送者: q363059309
2025-08-06 00:56:25 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="wx76fdd06dde311af3" sdkver="0">
		<title>偏爱</title>
		<des>张芸京</des>
		<type>3</type>
		<url>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</url>
		<lowurl>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</lowurl>
		<dataurl>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</dataurl>
		<lowdataurl>https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/</lowdataurl>
		<songalbumurl />
		<songlyric>[00:15.75]把昨天都作废现在你在我眼前[00:22.79]我想爱请给我机会[00:29.95]如果我错了也承担[00:33.31]认定你就是答案[00:37.99]我不怕谁嘲笑我极端[00:43.15]相信自己的直觉[00:46.99]顽固的人不喊累[00:50.43]爱上你我不撤退[00:56.03]我说过我不闪躲我非要这么做[01:02.03]讲不听也偏要爱[01:04.39]更努力爱让你明白[01:09.75]没有别条路能走[01:12.75]你决定要不要陪我[01:15.75]讲不听偏爱靠我感觉爱[01:19.15]等你的依赖对你偏爱[01:29.43]痛也很愉快[01:37.99]把昨天都作废现在你在我眼前[01:45.07]我想爱请给我机会[01:51.71]如果我错了也承担[01:55.55]认定你就是答案[02:00.31]我不怕谁嘲笑我极端[02:05.43]相信自己的直觉[02:09.27]顽固的人不喊累[02:12.75]爱上你我不撤退[02:18.31]我说过我不闪躲我非要这么做[02:24.35]讲不听也偏要爱[02:26.63]更努力爱让你明白[02:32.03]没有别条路能走[02:35.07]你决定要不要陪我[02:38.07]讲不听偏爱靠我感觉爱[02:41.47]等你的依赖[02:44.43]不后悔有把握[02:47.23]我不闪躲我非要这么做[02:51.71]讲不听也偏要爱[02:54.07]更努力爱让你明白[02:59.95]没有别条路能走[03:02.47]你决定要不要陪我[03:05.47]讲不听偏爱靠我感觉爱[03:08.87]等你的依赖对你偏爱[03:19.15]痛也很愉快</songlyric>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>q363059309</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>132</version>
		<appname>抖音短视频</appname>
	</appinfo>
	<commenturl />
</msg>

2025-08-06 00:56:25 | DEBUG | XML消息类型: 3
2025-08-06 00:56:25 | DEBUG | XML消息标题: 偏爱
2025-08-06 00:56:25 | DEBUG | XML消息描述: 张芸京
2025-08-06 00:56:25 | DEBUG | XML消息URL: https://v26-luna.douyinvod.com/c1f6dea842156f10a691d8e2be352237/68939363/video/tos/cn/tos-cn-ve-2774/oIniQokLMB9IFCFZlBeMydMQGptigYfSi3XDEk/
2025-08-06 00:56:25 | INFO | 收到红包消息: 标题:偏爱 描述:张芸京 来自:47325400669@chatroom 发送人:wxid_4usgcju5ey9q29
2025-08-06 00:56:27 | DEBUG | 收到消息: {'MsgId': 1979182856, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1jjimgid98no12:\n@郭\u2005\n🎶----- 找到以下歌曲 -----🎶\n1. 🎵 偏爱（0.8x） - WongKarKwoo 🎤\n2. 🎵 偏爱 - 王大毛呀呀呀松小魔王 🎤\n3. 🎵 偏爱 - 杜温尼尔 🎤\n4. 🎵 偏爱 - 张芸京 🎤\n5. 🎵 偏爱（DJ抖音版） - 猛宫 🎤\n_________________________\n🎵输入 “播放 + 序号” 播放歌曲🎵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412999, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ubbh6q832tcs21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_7xvPsYbn|v1_+KWBPltv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ting : @郭\u2005\n\ue326----- 找到以下歌曲 -----\ue326\n1. \ue03e 偏爱（0.8x） - WongKarKwoo \ue03c...', 'NewMsgId': 9066576418725074663, 'MsgSeq': 871430453}
2025-08-06 00:56:27 | INFO | 收到文本消息: 消息ID:1979182856 来自:47325400669@chatroom 发送人:wxid_1jjimgid98no12 @:['wxid_ubbh6q832tcs21'] 内容:@郭 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 偏爱（0.8x） - WongKarKwoo 🎤
2. 🎵 偏爱 - 王大毛呀呀呀松小魔王 🎤
3. 🎵 偏爱 - 杜温尼尔 🎤
4. 🎵 偏爱 - 张芸京 🎤
5. 🎵 偏爱（DJ抖音版） - 猛宫 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵
2025-08-06 00:56:27 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@郭 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 偏爱（0.8x） - WongKarKwoo 🎤
2. 🎵 偏爱 - 王大毛呀呀呀松小魔王 🎤
3. 🎵 偏爱 - 杜温尼尔 🎤
4. 🎵 偏爱 - 张芸京 🎤
5. 🎵 偏爱（DJ抖音版） - 猛宫 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵' from wxid_1jjimgid98no12 in 47325400669@chatroom
2025-08-06 00:56:27 | DEBUG | [DouBaoImageToImage] 命令解析: ['@郭\u2005\n🎶-----', '找到以下歌曲', '-----🎶\n1. 🎵 偏爱（0.8x） - WongKarKwoo 🎤\n2. 🎵 偏爱 - 王大毛呀呀呀松小魔王 🎤\n3. 🎵 偏爱 - 杜温尼尔 🎤\n4. 🎵 偏爱 - 张芸京 🎤\n5. 🎵 偏爱（DJ抖音版） - 猛宫 🎤\n_________________________\n🎵输入 “播放 + 序号” 播放歌曲🎵']
2025-08-06 00:56:27 | DEBUG | 处理消息内容: '@郭 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 偏爱（0.8x） - WongKarKwoo 🎤
2. 🎵 偏爱 - 王大毛呀呀呀松小魔王 🎤
3. 🎵 偏爱 - 杜温尼尔 🎤
4. 🎵 偏爱 - 张芸京 🎤
5. 🎵 偏爱（DJ抖音版） - 猛宫 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵'
2025-08-06 00:56:27 | DEBUG | 消息内容 '@郭 
🎶----- 找到以下歌曲 -----🎶
1. 🎵 偏爱（0.8x） - WongKarKwoo 🎤
2. 🎵 偏爱 - 王大毛呀呀呀松小魔王 🎤
3. 🎵 偏爱 - 杜温尼尔 🎤
4. 🎵 偏爱 - 张芸京 🎤
5. 🎵 偏爱（DJ抖音版） - 猛宫 🎤
_________________________
🎵输入 “播放 + 序号” 播放歌曲🎵' 不匹配任何命令，忽略
2025-08-06 00:56:30 | DEBUG | 收到消息: {'MsgId': 1359123639, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>4750203700754884609</newmsgid>\n    <sec_msg_node>\n      <sfn>0</sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len>0</clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[0]]></fold-reduce>\n      <block-range>1</block-range>\n      <media-to-emoji>0</media-to-emoji>\n      <bubble-type>1</bubble-type>\n      <preview-type>0</preview-type>\n      <url-click-type>0</url-click-type>\n      <sec-ctrl-flag>0</sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag>0</risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412997, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6589108876446420897, 'MsgSeq': 871430454}
2025-08-06 00:56:30 | DEBUG | 系统消息类型: secmsg
2025-08-06 00:56:30 | INFO | 未知的系统消息类型: {'MsgId': 1359123639, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>4750203700754884609</newmsgid>\n    <sec_msg_node>\n      <sfn>0</sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len>0</clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[0]]></fold-reduce>\n      <block-range>1</block-range>\n      <media-to-emoji>0</media-to-emoji>\n      <bubble-type>1</bubble-type>\n      <preview-type>0</preview-type>\n      <url-click-type>0</url-click-type>\n      <sec-ctrl-flag>0</sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag>0</risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754412997, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6589108876446420897, 'MsgSeq': 871430454, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-08-06 00:56:33 | DEBUG | 收到消息: {'MsgId': 1418840727, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>群聊的聊天记录</title>\n\t\t<des>阿猪米德: 🎵 歌曲列表 🎵\n\n 1. 《偏爱》 - 张芸京\n 2. 《偏爱 (Live)》 - Lil Ghost小鬼\n 3. 《偏爱》 - 半吨兄弟\n 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京\n 5. 《偏爱》 - 杨朵\n 6. 《偏爱》 - Lil Ghost小鬼\n 7. 《偏爱 (Live)》 - 占二曦\n 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill\n 9. 《偏爱 (Live)》 - 张芸京\n10. 《偏爱 (DJ纯享版)》 - DJ宝儿\n11. 《偏爱》 - 晏明修\n12. 《偏爱 (Live)》 - 四只烤翅\n13. 《偏爱 (温柔版)》 - ycccc\n14. 《偏爱》 - 小阿枫\n15. 《偏爱》 - 吴雨霏/邓丽欣\n16. 《偏爱 (钢琴版)》 - JESSE T\n17. 《偏爱》 - 傅菁\n18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野\n19. 《偏爱》 - 云汐\n20. 《偏爱 (唯美钢琴版)》 - 李好\n\n💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</des>\n\t\t<action />\n\t\t<type>19</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<recorditem><![CDATA[<recordinfo><fromscene>0</fromscene><favcreatetime>1754413000</favcreatetime><isChatRoom>0</isChatRoom><title>群聊的聊天记录</title><desc>阿猪米德: 🎵 歌曲列表 🎵\n\n 1. 《偏爱》 - 张芸京\n 2. 《偏爱 (Live)》 - Lil Ghost小鬼\n 3. 《偏爱》 - 半吨兄弟\n 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京\n 5. 《偏爱》 - 杨朵\n 6. 《偏爱》 - Lil Ghost小鬼\n 7. 《偏爱 (Live)》 - 占二曦\n 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill\n 9. 《偏爱 (Live)》 - 张芸京\n10. 《偏爱 (DJ纯享版)》 - DJ宝儿\n11. 《偏爱》 - 晏明修\n12. 《偏爱 (Live)》 - 四只烤翅\n13. 《偏爱 (温柔版)》 - ycccc\n14. 《偏爱》 - 小阿枫\n15. 《偏爱》 - 吴雨霏/邓丽欣\n16. 《偏爱 (钢琴版)》 - JESSE T\n17. 《偏爱》 - 傅菁\n18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野\n19. 《偏爱》 - 云汐\n20. 《偏爱 (唯美钢琴版)》 - 李好\n\n💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</desc><datalist count="1"><dataitem datatype="1" dataid="4fd12216c3f120ddf9eed8b2616a6863" htmlid="4fd12216c3f120ddf9eed8b2616a6863"><sourcename>阿猪米德</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/FwBha00mTQXVxz0EjZy3g8CeicEAIujXyGHVE9Qialib8EH86Fz1neUeZbs3Qp757tIestENic7oeQ9s36X7rjQo8INFvJ4qD554TyeibtUgCalibSiaReThicr1ux0Gb0QlPsQv/132</sourceheadurl><sourcetime>2025-08-06 00:56:40</sourcetime><datadesc>🎵 歌曲列表 🎵\n\n 1. 《偏爱》 - 张芸京\n 2. 《偏爱 (Live)》 - Lil Ghost小鬼\n 3. 《偏爱》 - 半吨兄弟\n 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京\n 5. 《偏爱》 - 杨朵\n 6. 《偏爱》 - Lil Ghost小鬼\n 7. 《偏爱 (Live)》 - 占二曦\n 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill\n 9. 《偏爱 (Live)》 - 张芸京\n10. 《偏爱 (DJ纯享版)》 - DJ宝儿\n11. 《偏爱》 - 晏明修\n12. 《偏爱 (Live)》 - 四只烤翅\n13. 《偏爱 (温柔版)》 - ycccc\n14. 《偏爱》 - 小阿枫\n15. 《偏爱》 - 吴雨霏/邓丽欣\n16. 《偏爱 (钢琴版)》 - JESSE T\n17. 《偏爱》 - 傅菁\n18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野\n19. 《偏爱》 - 云汐\n20. 《偏爱 (唯美钢琴版)》 - 李好\n\n💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</datadesc><srcMsgLocalid>1754413000</srcMsgLocalid><srcMsgCreateTime>1754413000</srcMsgCreateTime><fromnewmsgid>1754413000000</fromnewmsgid><dataitemsource><hashusername>24d1e77dc3af35ffd7dfc00d46657cbdb61050e84b921d2feff80f7cc1eb936e</hashusername></dataitemsource></dataitem></datalist></recordinfo>]]></recorditem>\n\t</appmsg>\n\t<fromusername>wxid_fh84okl6f5wp22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413005, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<passthrough>\n\t\t<forward_depth>0</forward_depth>\n\t</passthrough>\n\t<sec_msg_node>\n\t\t<uuid>dceecceca697b2db0c5f9bb213a4c707_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_Y2+J3k9R|v1_SAItAgbi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : [聊天记录]', 'NewMsgId': 2978451018239103086, 'MsgSeq': 871430455}
2025-08-06 00:56:33 | DEBUG | 从群聊消息中提取发送者: wxid_fh84okl6f5wp22
2025-08-06 00:56:33 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>群聊的聊天记录</title>
		<des>阿猪米德: 🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</des>
		<action />
		<type>19</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5></emoticonmd5>
			<fileext />
			<aeskey></aeskey>
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl />
		<md5 />
		<statextstr />
		<recorditem><![CDATA[<recordinfo><fromscene>0</fromscene><favcreatetime>1754413000</favcreatetime><isChatRoom>0</isChatRoom><title>群聊的聊天记录</title><desc>阿猪米德: 🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</desc><datalist count="1"><dataitem datatype="1" dataid="4fd12216c3f120ddf9eed8b2616a6863" htmlid="4fd12216c3f120ddf9eed8b2616a6863"><sourcename>阿猪米德</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/FwBha00mTQXVxz0EjZy3g8CeicEAIujXyGHVE9Qialib8EH86Fz1neUeZbs3Qp757tIestENic7oeQ9s36X7rjQo8INFvJ4qD554TyeibtUgCalibSiaReThicr1ux0Gb0QlPsQv/132</sourceheadurl><sourcetime>2025-08-06 00:56:40</sourcetime><datadesc>🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</datadesc><srcMsgLocalid>1754413000</srcMsgLocalid><srcMsgCreateTime>1754413000</srcMsgCreateTime><fromnewmsgid>1754413000000</fromnewmsgid><dataitemsource><hashusername>24d1e77dc3af35ffd7dfc00d46657cbdb61050e84b921d2feff80f7cc1eb936e</hashusername></dataitemsource></dataitem></datalist></recordinfo>]]></recorditem>
	</appmsg>
	<fromusername>wxid_fh84okl6f5wp22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-06 00:56:33 | DEBUG | XML消息类型: 19
2025-08-06 00:56:33 | DEBUG | XML消息标题: 群聊的聊天记录
2025-08-06 00:56:33 | DEBUG | XML消息描述: 阿猪米德: 🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"
2025-08-06 00:56:33 | DEBUG | 附件信息 totallen: 0
2025-08-06 00:56:33 | INFO | 未知的XML消息类型: 19
2025-08-06 00:56:33 | INFO | 消息标题: 群聊的聊天记录
2025-08-06 00:56:33 | INFO | 消息描述: 阿猪米德: 🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"
2025-08-06 00:56:33 | INFO | 消息URL: N/A
2025-08-06 00:56:33 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>群聊的聊天记录</title>
		<des>阿猪米德: 🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</des>
		<action />
		<type>19</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5></emoticonmd5>
			<fileext />
			<aeskey></aeskey>
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl />
		<md5 />
		<statextstr />
		<recorditem><![CDATA[<recordinfo><fromscene>0</fromscene><favcreatetime>1754413000</favcreatetime><isChatRoom>0</isChatRoom><title>群聊的聊天记录</title><desc>阿猪米德: 🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</desc><datalist count="1"><dataitem datatype="1" dataid="4fd12216c3f120ddf9eed8b2616a6863" htmlid="4fd12216c3f120ddf9eed8b2616a6863"><sourcename>阿猪米德</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/FwBha00mTQXVxz0EjZy3g8CeicEAIujXyGHVE9Qialib8EH86Fz1neUeZbs3Qp757tIestENic7oeQ9s36X7rjQo8INFvJ4qD554TyeibtUgCalibSiaReThicr1ux0Gb0QlPsQv/132</sourceheadurl><sourcetime>2025-08-06 00:56:40</sourcetime><datadesc>🎵 歌曲列表 🎵

 1. 《偏爱》 - 张芸京
 2. 《偏爱 (Live)》 - Lil Ghost小鬼
 3. 《偏爱》 - 半吨兄弟
 4. 《偏爱 (DJ小贤Funky Remix|咚鼓版)》 - 张芸京
 5. 《偏爱》 - 杨朵
 6. 《偏爱》 - Lil Ghost小鬼
 7. 《偏爱 (Live)》 - 占二曦
 8. 《偏爱 (降调版|0.8x降调版)》 - blue-kill
 9. 《偏爱 (Live)》 - 张芸京
10. 《偏爱 (DJ纯享版)》 - DJ宝儿
11. 《偏爱》 - 晏明修
12. 《偏爱 (Live)》 - 四只烤翅
13. 《偏爱 (温柔版)》 - ycccc
14. 《偏爱》 - 小阿枫
15. 《偏爱》 - 吴雨霏/邓丽欣
16. 《偏爱 (钢琴版)》 - JESSE T
17. 《偏爱》 - 傅菁
18. 《偏爱 (咚鼓版)》 - 林凉/无毒井野
19. 《偏爱》 - 云汐
20. 《偏爱 (唯美钢琴版)》 - 李好

💡 输入"点歌+内容+序号"播放歌曲，例如"点歌周杰伦1"</datadesc><srcMsgLocalid>1754413000</srcMsgLocalid><srcMsgCreateTime>1754413000</srcMsgCreateTime><fromnewmsgid>1754413000000</fromnewmsgid><dataitemsource><hashusername>24d1e77dc3af35ffd7dfc00d46657cbdb61050e84b921d2feff80f7cc1eb936e</hashusername></dataitemsource></dataitem></datalist></recordinfo>]]></recorditem>
	</appmsg>
	<fromusername>wxid_fh84okl6f5wp22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-08-06 00:58:57 | DEBUG | 收到消息: {'MsgId': 206822673, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n没找到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413149, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_xhc6lD8C|v1_HME1zSIH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3293912775452115379, 'MsgSeq': 871430456}
2025-08-06 00:58:57 | INFO | 收到文本消息: 消息ID:206822673 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:没找到
2025-08-06 00:58:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没找到' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 00:58:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['没找到']
2025-08-06 00:58:57 | DEBUG | 处理消息内容: '没找到'
2025-08-06 00:58:57 | DEBUG | 消息内容 '没找到' 不匹配任何命令，忽略
2025-08-06 01:00:33 | DEBUG | 收到消息: {'MsgId': 335491698, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n@￼\u2005async with httpx.AsyncClient() as client:\n                response = await client.get(\n                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",\n                    params={\n                        "msg": song_name,\n                        "n": "1",     # 直接指定选择第一首歌\n                        "type": "json"\n                    }\n                )'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413245, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_fjgby1dzvif021]]></atuserlist>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>358</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_QUAX+ZrB|v1_AxgHrap2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : @￼\u2005async with httpx.AsyncClient() as client:\n                response = await client.g...', 'NewMsgId': 1578806363669015237, 'MsgSeq': 871430457}
2025-08-06 01:00:33 | INFO | 收到文本消息: 消息ID:335491698 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:['wxid_fjgby1dzvif021'] 内容:@￼ async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": song_name,
                        "n": "1",     # 直接指定选择第一首歌
                        "type": "json"
                    }
                )
2025-08-06 01:00:33 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@￼ async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": song_name,
                        "n": "1",     # 直接指定选择第一首歌
                        "type": "json"
                    }
                )' from wxid_ubbh6q832tcs21 in 47325400669@chatroom
2025-08-06 01:00:33 | DEBUG | [DouBaoImageToImage] 命令解析: ['@￼\u2005async', 'with', 'httpx.AsyncClient() as client:\n                response = await client.get(\n                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",\n                    params={\n                        "msg": song_name,\n                        "n": "1",     # 直接指定选择第一首歌\n                        "type": "json"\n                    }\n                )']
2025-08-06 01:00:33 | DEBUG | 处理消息内容: '@￼ async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": song_name,
                        "n": "1",     # 直接指定选择第一首歌
                        "type": "json"
                    }
                )'
2025-08-06 01:00:33 | DEBUG | 消息内容 '@￼ async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": song_name,
                        "n": "1",     # 直接指定选择第一首歌
                        "type": "json"
                    }
                )' 不匹配任何命令，忽略
2025-08-06 01:01:10 | DEBUG | 收到消息: {'MsgId': 1589897940, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n那就没'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413282, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Yh1tS4Fp|v1_AeF51TcS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2181623528385450797, 'MsgSeq': 871430458}
2025-08-06 01:01:10 | INFO | 收到文本消息: 消息ID:1589897940 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:那就没
2025-08-06 01:01:10 | DEBUG | [DouBaoImageToImage] 收到文本消息: '那就没' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 01:01:10 | DEBUG | [DouBaoImageToImage] 命令解析: ['那就没']
2025-08-06 01:01:10 | DEBUG | 处理消息内容: '那就没'
2025-08-06 01:01:10 | DEBUG | 消息内容 '那就没' 不匹配任何命令，忽略
2025-08-06 01:03:35 | DEBUG | 收到消息: {'MsgId': 1339175078, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 成都'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413427, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>11</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_vC81xWou|v1_y487QHVN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 成都', 'NewMsgId': 6555491496506802029, 'MsgSeq': 871430459}
2025-08-06 01:03:35 | INFO | 收到文本消息: 消息ID:1339175078 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 成都
2025-08-06 01:03:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 成都' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 01:03:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '成都']
2025-08-06 01:03:35 | DEBUG | 处理消息内容: '语音点歌 成都'
2025-08-06 01:03:35 | DEBUG | 消息内容 '语音点歌 成都' 不匹配任何命令，忽略
2025-08-06 01:03:37 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '赵雷 - 成都 - （铃声）', 'singer': '皓月清风', 'url': 'http://ring.bssdlbig.kugou.com/0bd95d2a0cb4b5cea046f27052c10528.mp3'}}
2025-08-06 01:03:38 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 01:03:38 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 1579776 bytes
2025-08-06 01:03:42 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:98736 格式:mp3 音频base64略
2025-08-06 01:03:42 | INFO | [VoiceMusicPlugin] 成功发送音乐: 赵雷 - 成都 - （铃声） - 皓月清风
2025-08-06 01:10:25 | DEBUG | 收到消息: {'MsgId': 1292215943, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fjgby1dzvif021:\n0.0[社会社会]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413837, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_MdgGeVYH|v1_RfSkThW1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '￼ : 0.0[社会社会]', 'NewMsgId': 1632190605905001550, 'MsgSeq': 871430461}
2025-08-06 01:10:25 | INFO | 收到文本消息: 消息ID:1292215943 来自:47325400669@chatroom 发送人:wxid_fjgby1dzvif021 @:[] 内容:0.0[社会社会]
2025-08-06 01:10:25 | DEBUG | [DouBaoImageToImage] 收到文本消息: '0.0[社会社会]' from wxid_fjgby1dzvif021 in 47325400669@chatroom
2025-08-06 01:10:25 | DEBUG | [DouBaoImageToImage] 命令解析: ['0.0[社会社会]']
2025-08-06 01:10:25 | DEBUG | 处理消息内容: '0.0[社会社会]'
2025-08-06 01:10:25 | DEBUG | 消息内容 '0.0[社会社会]' 不匹配任何命令，忽略
2025-08-06 01:11:51 | DEBUG | 收到消息: {'MsgId': 1164233913, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 gogogo'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754413923, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>11</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_qneCQxIg|v1_Cjws9VS9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 gogogo', 'NewMsgId': 7303140948271972427, 'MsgSeq': 871430462}
2025-08-06 01:11:51 | INFO | 收到文本消息: 消息ID:1164233913 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 gogogo
2025-08-06 01:11:51 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 gogogo' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 01:11:51 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', 'gogogo']
2025-08-06 01:11:51 | DEBUG | 处理消息内容: '语音点歌 gogogo'
2025-08-06 01:11:51 | DEBUG | 消息内容 '语音点歌 gogogo' 不匹配任何命令，忽略
2025-08-06 01:11:53 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '刘耀文-加油加油gogogo', 'singer': 'yy', 'url': 'http://ring.bssdlbig.kugou.com/ad320ae644bb2a47238baf8135e172be.mp3'}}
2025-08-06 01:11:54 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 01:11:54 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 47975 bytes
2025-08-06 01:11:55 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:3004 格式:mp3 音频base64略
2025-08-06 01:11:55 | INFO | [VoiceMusicPlugin] 成功发送音乐: 刘耀文-加油加油gogogo - yy
2025-08-06 01:12:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 01:16:59 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-06 01:16:59 | DEBUG | 群成员变化检查完成
2025-08-06 01:22:11 | DEBUG | 收到消息: {'MsgId': 834392948, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_km22lf6rwa7c22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>至臻</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7124603772509576554</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>QvemiY¹_慕ؓ悦ؓ˒</displayname>\n\t\t\t<content>这个衣服有图鉴吗</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;848953118&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_jbrB3FoN|v1_56VUQWZS&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1754412404</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_km22lf6rwa7c22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754414543, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>aaea4f72a819e862056924acb94237d7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_uUf4Tqr8|v1_jc9ZgDMT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2131458584446604421, 'MsgSeq': 871430465}
2025-08-06 01:22:11 | DEBUG | 从群聊消息中提取发送者: wxid_km22lf6rwa7c22
2025-08-06 01:22:11 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 01:22:11 | INFO | 收到引用消息: 消息ID:834392948 来自:27852221909@chatroom 发送人:wxid_km22lf6rwa7c22 内容:至臻 引用类型:1
2025-08-06 01:22:11 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 01:22:11 | INFO | [DouBaoImageToImage] 消息内容: '至臻' from wxid_km22lf6rwa7c22 in 27852221909@chatroom
2025-08-06 01:22:11 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['至臻']
2025-08-06 01:22:11 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 01:22:11 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 01:22:11 | INFO |   - 消息内容: 至臻
2025-08-06 01:22:11 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 01:22:11 | INFO |   - 发送人: wxid_km22lf6rwa7c22
2025-08-06 01:22:11 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '这个衣服有图鉴吗', 'Msgid': '7124603772509576554', 'NewMsgId': '7124603772509576554', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'QvemiY¹_慕ؓ悦ؓ˒', 'MsgSource': '<msgsource><sequence_id>848953118</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_jbrB3FoN|v1_56VUQWZS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754412404', 'SenderWxid': 'wxid_km22lf6rwa7c22'}
2025-08-06 01:22:11 | INFO |   - 引用消息ID: 
2025-08-06 01:22:11 | INFO |   - 引用消息类型: 
2025-08-06 01:22:11 | INFO |   - 引用消息内容: 这个衣服有图鉴吗
2025-08-06 01:22:11 | INFO |   - 引用消息发送人: wxid_km22lf6rwa7c22
2025-08-06 01:23:35 | DEBUG | 收到消息: {'MsgId': 782448154, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_km22lf6rwa7c22:\n没有图鉴'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754414627, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_T065Dp0K|v1_EAtiFNS1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2157628401481358595, 'MsgSeq': 871430466}
2025-08-06 01:23:35 | INFO | 收到文本消息: 消息ID:782448154 来自:27852221909@chatroom 发送人:wxid_km22lf6rwa7c22 @:[] 内容:没有图鉴
2025-08-06 01:23:35 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没有图鉴' from wxid_km22lf6rwa7c22 in 27852221909@chatroom
2025-08-06 01:23:35 | DEBUG | [DouBaoImageToImage] 命令解析: ['没有图鉴']
2025-08-06 01:23:35 | DEBUG | 处理消息内容: '没有图鉴'
2025-08-06 01:23:35 | DEBUG | 消息内容 '没有图鉴' 不匹配任何命令，忽略
2025-08-06 01:23:39 | DEBUG | 收到消息: {'MsgId': 383601906, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_km22lf6rwa7c22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a36553a8d293ab69da2d5f098b685688" encryver="1" cdnthumbaeskey="a36553a8d293ab69da2d5f098b685688" cdnthumburl="3057020100044b30490201000204da02ff8202032dce2e020430103673020468923e26042463303432316637392d656331332d346638372d386334362d643566633730386233386533020405250a020201000405004c4d9a00" cdnthumblength="3197" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204da02ff8202032dce2e020430103673020468923e26042463303432316637392d656331332d346638372d386334362d643566633730386233386533020405250a020201000405004c4d9a00" length="54776" md5="89a09bc3e660a8618113538f46a5c3df" hevc_mid_size="54776" originsourcemd5="85036f99ca13a7f3fb01034260d5d242">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754414630, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>53724cdaa938ecef9d04613cc736f4cf_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_HjiBcHzB|v1_xv0hrdhn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7075231575090516901, 'MsgSeq': 871430467}
2025-08-06 01:23:39 | INFO | 收到图片消息: 消息ID:383601906 来自:27852221909@chatroom 发送人:wxid_km22lf6rwa7c22 XML:<?xml version="1.0"?><msg><img aeskey="a36553a8d293ab69da2d5f098b685688" encryver="1" cdnthumbaeskey="a36553a8d293ab69da2d5f098b685688" cdnthumburl="3057020100044b30490201000204da02ff8202032dce2e020430103673020468923e26042463303432316637392d656331332d346638372d386334362d643566633730386233386533020405250a020201000405004c4d9a00" cdnthumblength="3197" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204da02ff8202032dce2e020430103673020468923e26042463303432316637392d656331332d346638372d386334362d643566633730386233386533020405250a020201000405004c4d9a00" length="54776" md5="89a09bc3e660a8618113538f46a5c3df" hevc_mid_size="54776" originsourcemd5="85036f99ca13a7f3fb01034260d5d242"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-08-06 01:23:39 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-08-06 01:23:39 | INFO | [TimerTask] 缓存图片消息: 383601906
2025-08-06 01:32:48 | DEBUG | 收到消息: {'MsgId': 1221513122, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>噢噢那我就不开了</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2157628401481358595</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_km22lf6rwa7c22</chatusr>\n\t\t\t<displayname>莫叫姐姐</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;149&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_3c8Lu/jT|v1_FdqJu8Ai&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n没有图鉴</content>\n\t\t\t<strid />\n\t\t\t<createtime>1754414627</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_2530z9t0joek22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754415180, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>be6e2c7e5741bb3dfc9dd10735ccdb15_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_n7xhM9gf|v1_MV5Vtg2j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6382460809323389910, 'MsgSeq': 871430468}
2025-08-06 01:32:48 | DEBUG | 从群聊消息中提取发送者: wxid_2530z9t0joek22
2025-08-06 01:32:48 | DEBUG | 使用已解析的XML处理引用消息
2025-08-06 01:32:48 | INFO | 收到引用消息: 消息ID:1221513122 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 内容:噢噢那我就不开了 引用类型:1
2025-08-06 01:32:48 | INFO | [DouBaoImageToImage] ========== 收到引用消息 ==========
2025-08-06 01:32:48 | INFO | [DouBaoImageToImage] 消息内容: '噢噢那我就不开了' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 01:32:48 | DEBUG | [DouBaoImageToImage] 引用命令解析: ['噢噢那我就不开了']
2025-08-06 01:32:48 | DEBUG | [DouBaoImageToImage] 不是图生图引用命令，跳过处理
2025-08-06 01:32:48 | INFO | [TimerTask] 收到引用消息调试信息:
2025-08-06 01:32:48 | INFO |   - 消息内容: 噢噢那我就不开了
2025-08-06 01:32:48 | INFO |   - 群组ID: 27852221909@chatroom
2025-08-06 01:32:48 | INFO |   - 发送人: wxid_2530z9t0joek22
2025-08-06 01:32:48 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n没有图鉴', 'Msgid': '2157628401481358595', 'NewMsgId': '2157628401481358595', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '莫叫姐姐', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_3c8Lu/jT|v1_FdqJu8Ai</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1754414627', 'SenderWxid': 'wxid_2530z9t0joek22'}
2025-08-06 01:32:48 | INFO |   - 引用消息ID: 
2025-08-06 01:32:48 | INFO |   - 引用消息类型: 
2025-08-06 01:32:48 | INFO |   - 引用消息内容: 
没有图鉴
2025-08-06 01:32:48 | INFO |   - 引用消息发送人: wxid_2530z9t0joek22
2025-08-06 01:32:57 | DEBUG | 收到消息: {'MsgId': 1828423269, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n没图鉴不要了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754415189, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_T7+26nZ+|v1_utpUVwWL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1532452496598756673, 'MsgSeq': 871430469}
2025-08-06 01:32:57 | INFO | 收到文本消息: 消息ID:1828423269 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:没图鉴不要了
2025-08-06 01:32:57 | DEBUG | [DouBaoImageToImage] 收到文本消息: '没图鉴不要了' from wxid_2530z9t0joek22 in 27852221909@chatroom
2025-08-06 01:32:57 | DEBUG | [DouBaoImageToImage] 命令解析: ['没图鉴不要了']
2025-08-06 01:32:57 | DEBUG | 处理消息内容: '没图鉴不要了'
2025-08-06 01:32:57 | DEBUG | 消息内容 '没图鉴不要了' 不匹配任何命令，忽略
2025-08-06 01:35:00 | INFO | [TimerTask] 清理过期图片缓存: 1个
2025-08-06 01:36:00 | INFO | [TimerTask] 清理过期图片缓存: 1个
2025-08-06 01:42:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 01:47:00 | INFO | [TimerTask] 清理过期图片缓存: 1个
2025-08-06 02:02:15 | DEBUG | 收到消息: {'MsgId': 974926606, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开呀'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754416947, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_tOex+tHE|v1_N6kaDq6y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5191364325500846990, 'MsgSeq': 871430470}
2025-08-06 02:02:15 | INFO | 收到文本消息: 消息ID:974926606 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开呀
2025-08-06 02:02:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '开呀' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 02:02:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['开呀']
2025-08-06 02:02:15 | DEBUG | 处理消息内容: '开呀'
2025-08-06 02:02:15 | DEBUG | 消息内容 '开呀' 不匹配任何命令，忽略
2025-08-06 02:02:28 | DEBUG | 收到消息: {'MsgId': 837395188, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n后边会出的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754416960, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_Dh7o5S7L|v1_ofe7HbxF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1386826760921308948, 'MsgSeq': 871430471}
2025-08-06 02:02:28 | INFO | 收到文本消息: 消息ID:837395188 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:后边会出的
2025-08-06 02:02:28 | DEBUG | [DouBaoImageToImage] 收到文本消息: '后边会出的' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 02:02:28 | DEBUG | [DouBaoImageToImage] 命令解析: ['后边会出的']
2025-08-06 02:02:28 | DEBUG | 处理消息内容: '后边会出的'
2025-08-06 02:02:28 | DEBUG | 消息内容 '后边会出的' 不匹配任何命令，忽略
2025-08-06 02:10:15 | DEBUG | 收到消息: {'MsgId': 966602327, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 小三'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417427, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_/de2IVzR|v1_D8yjgX3w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 小三', 'NewMsgId': 8607612029964062418, 'MsgSeq': 871430472}
2025-08-06 02:10:15 | INFO | 收到文本消息: 消息ID:966602327 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 小三
2025-08-06 02:10:15 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 小三' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 02:10:15 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '小三']
2025-08-06 02:10:15 | DEBUG | 处理消息内容: '语音点歌 小三'
2025-08-06 02:10:15 | DEBUG | 消息内容 '语音点歌 小三' 不匹配任何命令，忽略
2025-08-06 02:10:18 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '晚秋（粤语女声版）−赵十三', 'singer': '曲奇小伙伴', 'url': 'http://ring.bssdlbig.kugou.com/602c7f875d7b2867cbb7cbae00b85093.mp3'}}
2025-08-06 02:10:19 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 02:10:19 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 745162 bytes
2025-08-06 02:10:21 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:46576 格式:mp3 音频base64略
2025-08-06 02:10:21 | INFO | [VoiceMusicPlugin] 成功发送音乐: 晚秋（粤语女声版）−赵十三 - 曲奇小伙伴
2025-08-06 02:10:41 | DEBUG | 收到消息: {'MsgId': 1318231519, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 来财'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417453, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_SRPEDjoq|v1_jfgPyiag</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 来财', 'NewMsgId': 1136940083513336035, 'MsgSeq': 871430475}
2025-08-06 02:10:41 | INFO | 收到文本消息: 消息ID:1318231519 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 来财
2025-08-06 02:10:41 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 来财' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 02:10:41 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '来财']
2025-08-06 02:10:41 | DEBUG | 处理消息内容: '语音点歌 来财'
2025-08-06 02:10:41 | DEBUG | 消息内容 '语音点歌 来财' 不匹配任何命令，忽略
2025-08-06 02:10:43 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '短信一来，恭喜发财', 'singer': '飞001', 'url': 'http://ring.bssdlbig.kugou.com/50706af270a22df502702379e582a43a.mp3'}}
2025-08-06 02:10:44 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 02:10:44 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 50220 bytes
2025-08-06 02:10:44 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:3097 格式:mp3 音频base64略
2025-08-06 02:10:44 | INFO | [VoiceMusicPlugin] 成功发送音乐: 短信一来，恭喜发财 - 飞001
2025-08-06 02:11:04 | DEBUG | 收到消息: {'MsgId': 74954288, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 求佛'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417477, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_Iq6q9dxK|v1_SEzyrn28</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 求佛', 'NewMsgId': 2114533870175891321, 'MsgSeq': 871430478}
2025-08-06 02:11:04 | INFO | 收到文本消息: 消息ID:74954288 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 求佛
2025-08-06 02:11:04 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 求佛' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 02:11:04 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '求佛']
2025-08-06 02:11:04 | DEBUG | 处理消息内容: '语音点歌 求佛'
2025-08-06 02:11:04 | DEBUG | 消息内容 '语音点歌 求佛' 不匹配任何命令，忽略
2025-08-06 02:11:06 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '求佛', 'singer': '王强', 'url': 'http://ring.bssdlbig.kugou.com/a7d8a317926e9a4a0789768976c0ccc9.mp3'}}
2025-08-06 02:11:07 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 02:11:07 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 324126 bytes
2025-08-06 02:11:09 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:40465 格式:mp3 音频base64略
2025-08-06 02:11:09 | INFO | [VoiceMusicPlugin] 成功发送音乐: 求佛 - 王强
2025-08-06 02:12:37 | DEBUG | 收到消息: {'MsgId': 1476765776, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 白鸽乌鸦相爱的戏码'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417570, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_jug1f2OW|v1_hRBiakVl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 白鸽乌鸦相爱的戏码', 'NewMsgId': 7610938946582394977, 'MsgSeq': 871430481}
2025-08-06 02:12:37 | INFO | 收到文本消息: 消息ID:1476765776 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 白鸽乌鸦相爱的戏码
2025-08-06 02:12:38 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 白鸽乌鸦相爱的戏码' from wxid_ubbh6q832tcs21 in 55878994168@chatroom
2025-08-06 02:12:38 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '白鸽乌鸦相爱的戏码']
2025-08-06 02:12:38 | DEBUG | 处理消息内容: '语音点歌 白鸽乌鸦相爱的戏码'
2025-08-06 02:12:38 | DEBUG | 消息内容 '语音点歌 白鸽乌鸦相爱的戏码' 不匹配任何命令，忽略
2025-08-06 02:12:39 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '潘成（皮卡潘） - 白鸽乌鸦相爱的戏码', 'singer': 'め゛凉薄之心＊', 'url': 'http://ring.bssdlbig.kugou.com/862d98c38ce0cb9e102b0b9fe10dd71f.mp3'}}
2025-08-06 02:12:40 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 02:12:40 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 522096 bytes
2025-08-06 02:12:42 | INFO | 发送语音消息: 对方wxid:55878994168@chatroom 时长:32589 格式:mp3 音频base64略
2025-08-06 02:12:42 | INFO | [VoiceMusicPlugin] 成功发送音乐: 潘成（皮卡潘） - 白鸽乌鸦相爱的戏码 - め゛凉薄之心＊
2025-08-06 02:12:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 02:13:12 | DEBUG | 收到消息: {'MsgId': 442638679, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n语音点歌 白鸽乌鸦相爱的戏码'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417604, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>14</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_2C8uF0gy|v1_nKC9owGN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 语音点歌 白鸽乌鸦相爱的戏码', 'NewMsgId': 3585845676226292157, 'MsgSeq': 871430484}
2025-08-06 02:13:12 | INFO | 收到文本消息: 消息ID:442638679 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:语音点歌 白鸽乌鸦相爱的戏码
2025-08-06 02:13:12 | DEBUG | [DouBaoImageToImage] 收到文本消息: '语音点歌 白鸽乌鸦相爱的戏码' from wxid_ubbh6q832tcs21 in 47325400669@chatroom
2025-08-06 02:13:12 | DEBUG | [DouBaoImageToImage] 命令解析: ['语音点歌', '白鸽乌鸦相爱的戏码']
2025-08-06 02:13:12 | DEBUG | 处理消息内容: '语音点歌 白鸽乌鸦相爱的戏码'
2025-08-06 02:13:12 | DEBUG | 消息内容 '语音点歌 白鸽乌鸦相爱的戏码' 不匹配任何命令，忽略
2025-08-06 02:13:13 | DEBUG | [VoiceMusicPlugin] API响应: {'code': 1000, 'data': {'songname': '潘成（皮卡潘） - 白鸽乌鸦相爱的戏码', 'singer': 'め゛凉薄之心＊', 'url': 'http://ring.bssdlbig.kugou.com/862d98c38ce0cb9e102b0b9fe10dd71f.mp3'}}
2025-08-06 02:13:14 | DEBUG | [VoiceMusicPlugin] 音频文件类型: audio/mpeg
2025-08-06 02:13:14 | INFO | [VoiceMusicPlugin] 成功下载音频文件: 522096 bytes
2025-08-06 02:13:15 | INFO | 发送语音消息: 对方wxid:47325400669@chatroom 时长:32589 格式:mp3 音频base64略
2025-08-06 02:13:15 | INFO | [VoiceMusicPlugin] 成功发送音乐: 潘成（皮卡潘） - 白鸽乌鸦相爱的戏码 - め゛凉薄之心＊
2025-08-06 02:13:16 | DEBUG | 收到消息: {'MsgId': 1003635568, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': "wxid_1jjimgid98no12:\n音色 '点歌' 不存在！\n发送 '语音菜单' 查看可用音色列表"}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417606, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_w47s4cgO|v1_HtZW0qaq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': "ting : 音色 '点歌' 不存在！\n发送 '语音菜单' 查看可用音色列表", 'NewMsgId': 3792747129884955525, 'MsgSeq': 871430485}
2025-08-06 02:13:16 | INFO | 收到文本消息: 消息ID:1003635568 来自:47325400669@chatroom 发送人:wxid_1jjimgid98no12 @:[] 内容:音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表
2025-08-06 02:13:16 | DEBUG | [DouBaoImageToImage] 收到文本消息: '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表' from wxid_1jjimgid98no12 in 47325400669@chatroom
2025-08-06 02:13:16 | DEBUG | [DouBaoImageToImage] 命令解析: ['音色', "'点歌'", "不存在！\n发送 '语音菜单' 查看可用音色列表"]
2025-08-06 02:13:16 | DEBUG | 处理消息内容: '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表'
2025-08-06 02:13:16 | DEBUG | 消息内容 '音色 '点歌' 不存在！
发送 '语音菜单' 查看可用音色列表' 不匹配任何命令，忽略
2025-08-06 02:13:18 | DEBUG | 收到消息: {'MsgId': 213104860, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n@郭\u2005\n此模型API密钥未配置，请联系管理员'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417606, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ubbh6q832tcs21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_mixuy510|v1_wGLW0xc/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : @郭\u2005\n此模型API密钥未配置，请联系管理员', 'NewMsgId': 1776547109332953913, 'MsgSeq': 871430486}
2025-08-06 02:13:18 | INFO | 收到文本消息: 消息ID:213104860 来自:47325400669@chatroom 发送人:heaventt @:['wxid_ubbh6q832tcs21'] 内容:@郭 
此模型API密钥未配置，请联系管理员
2025-08-06 02:13:18 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@郭 
此模型API密钥未配置，请联系管理员' from heaventt in 47325400669@chatroom
2025-08-06 02:13:18 | DEBUG | [DouBaoImageToImage] 命令解析: ['@郭\u2005\n此模型API密钥未配置，请联系管理员']
2025-08-06 02:13:18 | DEBUG | 处理消息内容: '@郭 
此模型API密钥未配置，请联系管理员'
2025-08-06 02:13:18 | DEBUG | 消息内容 '@郭 
此模型API密钥未配置，请联系管理员' 不匹配任何命令，忽略
2025-08-06 02:13:20 | DEBUG | 收到消息: {'MsgId': 1708552002, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n@ting\u2005\n此模型API密钥未配置，请联系管理员'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754417608, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_1jjimgid98no12</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>0</silence>\n\t<membercount>224</membercount>\n\t<signature>N0_V1_a0uVRw6W|v1_dsWK2kKL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : @ting\u2005\n此模型API密钥未配置，请联系管理员', 'NewMsgId': 6755360837630919193, 'MsgSeq': 871430489}
2025-08-06 02:13:20 | INFO | 收到文本消息: 消息ID:1708552002 来自:47325400669@chatroom 发送人:heaventt @:['wxid_1jjimgid98no12'] 内容:@ting 
此模型API密钥未配置，请联系管理员
2025-08-06 02:13:20 | DEBUG | [DouBaoImageToImage] 收到文本消息: '@ting 
此模型API密钥未配置，请联系管理员' from heaventt in 47325400669@chatroom
2025-08-06 02:13:20 | DEBUG | [DouBaoImageToImage] 命令解析: ['@ting\u2005\n此模型API密钥未配置，请联系管理员']
2025-08-06 02:13:20 | DEBUG | 处理消息内容: '@ting 
此模型API密钥未配置，请联系管理员'
2025-08-06 02:13:20 | DEBUG | 消息内容 '@ting 
此模型API密钥未配置，请联系管理员' 不匹配任何命令，忽略
2025-08-06 02:16:59 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-06 02:17:00 | DEBUG | 群成员变化检查完成
2025-08-06 02:24:00 | INFO | [TimerTask] 清理过期图片缓存: 1个
2025-08-06 02:37:42 | DEBUG | 收到消息: {'MsgId': 1985966473, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754419074, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>149</membercount>\n\t<signature>N0_V1_mB9C1A5v|v1_MBI0GJ9a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3123126298971213268, 'MsgSeq': 871430490}
2025-08-06 02:37:42 | INFO | 收到文本消息: 消息ID:1985966473 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:唱舞签到
2025-08-06 02:37:42 | INFO | 发送链接消息: 对方wxid:27852221909@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-08-06 02:37:44 | INFO | 发送app消息: 对方wxid:27852221909@chatroom 类型:33 xml:<appmsg appid="wxa708de63ee4a2353" sdkver="0"><title>派对邀请函-签到解锁6周年专属称号</title><des>唱舞星愿站</des><username /><action>view</action><type>33</type><showtype>0</showtype><content /><url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url><lowurl /><forwardflag>0</forwardflag><dataurl /><lowdataurl /><contentattr>0</contentattr><appattach>    <attachid />    <cdnthumburl>3057020100044b30490201000204a95c809d02032df9270204a30893240204688d5d7f042461363063363963652d646365362d343865622d626464632d3866336366663533306631610204051408030201000405004c55cf00</cdnthumburl>    <cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>    <cdnthumblength>173353</cdnthumblength>    <cdnthumbheight>576</cdnthumbheight>    <cdnthumbwidth>720</cdnthumbwidth>    <cdnthumbaeskey>f10bff006983850491a301a4cdb0c357</cdnthumbaeskey>    <aeskey>f10bff006983850491a301a4cdb0c357</aeskey>    <encryver>1</encryver>    <fileext />    <islargefilemsg>0</islargefilemsg></appattach><extinfo /><androidsource>3</androidsource><sourceusername>gh_25eb09d7bc53@app</sourceusername><sourcedisplayname>唱舞星愿站</sourcedisplayname><commenturl /><thumburl /><mediatagname /><messageaction><![CDATA[]]></messageaction><messageext><![CDATA[]]></messageext><weappinfo>    <pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>    <username>gh_25eb09d7bc53@app</username>    <appid>wxa708de63ee4a2353</appid>    <version>16</version>    <type>2</type>    <weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>    <weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>    <shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>    <appservicetype>0</appservicetype>    <secflagforsinglepagemode>0</secflagforsinglepagemode>    <videopageinfo>        <thumbwidth>720</thumbwidth>        <thumbheight>576</thumbheight>        <fromopensdk>0</fromopensdk>    </videopageinfo>    <wxaTradeCommentScore>0</wxaTradeCommentScore></weappinfo><statextstr /><md5>69b76813fe0b2a04149aee01978e42f7</md5></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl></commenturl>
2025-08-06 02:37:44 | INFO | [DanceSignIn] 小程序消息发送成功，msg_id: 5704154109462429672
2025-08-06 02:37:44 | DEBUG | [DouBaoImageToImage] 收到文本消息: '唱舞签到' from wxid_ubbh6q832tcs21 in 27852221909@chatroom
2025-08-06 02:37:44 | DEBUG | [DouBaoImageToImage] 命令解析: ['唱舞签到']
2025-08-06 02:37:44 | DEBUG | 处理消息内容: '唱舞签到'
2025-08-06 02:37:44 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-08-06 02:42:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 02:57:47 | DEBUG | 收到消息: {'MsgId': 318155479, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 9999, 'Content': {'string': '<newcount>3</newcount><version>900</version>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754420280, 'MsgSource': '<msgsource>\n\t<signature>v1_bjyqUNaa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4654002541328034085, 'MsgSeq': 871430495}
2025-08-06 02:57:47 | INFO | 未知的消息类型: {'MsgId': 318155479, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 9999, 'Content': {'string': '<newcount>3</newcount><version>900</version>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754420280, 'MsgSource': '<msgsource>\n\t<signature>v1_bjyqUNaa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4654002541328034085, 'MsgSeq': 871430495, 'FromWxid': 'weixin'}
2025-08-06 02:57:50 | DEBUG | 收到消息: {'MsgId': 1858506045, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="195" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1754420283, 'MsgSource': '<msgsource>\n\t<signature>v1_tzr9jbkL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8861870035372181666, 'MsgSeq': 871430497}
2025-08-06 03:12:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 03:16:59 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-06 03:17:00 | DEBUG | 群成员变化检查完成
2025-08-06 03:42:58 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 04:12:59 | INFO | [TempFileManager] 开始清理临时文件...
2025-08-06 04:16:59 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-08-06 04:17:00 | DEBUG | 群成员变化检查完成
